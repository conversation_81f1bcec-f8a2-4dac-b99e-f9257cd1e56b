package com.center.infrastructure.system.biz.user.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class UserResp{

  @Schema(description = "用户ID",example = "1")
  private Long id;

  @Schema(description = "用户名称",example = "1")
  private String username;

  @Schema(description ="展示名")
  private String displayName;

  @Schema(description = "部门ID")
  private Long departId;

  @Schema(description = "角色id")
  private Long roleId;

  @Schema(description = "角色名称")
  private String roleName;

  @Schema(description = "邮箱",example = "<EMAIL>")
  private String email;

  @Schema(description = "手机号",example = "12345678910")
  private String phoneNumber;

  @Schema(description = "用户最后新一次登录地点IP",example = "1")
  private String loginIp;

  @Schema(description = "用户最新一次登录时间",example = "2024-08-19 15:30:12")
  private LocalDateTime loginTime;
}
