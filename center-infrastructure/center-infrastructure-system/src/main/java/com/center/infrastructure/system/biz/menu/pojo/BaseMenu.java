package com.center.infrastructure.system.biz.menu.pojo;

import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.infrastructure.system.biz.menu.enumerate.MenuCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public abstract class BaseMenu {

  @Schema(description = "菜单名称",requiredMode = RequiredMode.REQUIRED,example = "用户管理")
  @Length(min = 2,max = 20,message = "菜单名称长度为2~10个字符")
  private String menuName;

  @Schema(description = "菜单类型",example = "MENU")
  @EnumValidate(value = MenuCategoryEnum.class,message = "菜单类型不正确")
  @NotNull
  private String category;

  @Schema(description = "菜单排序",requiredMode = RequiredMode.REQUIRED,example = "1")
  @NotNull
  private Integer sort;

  @Schema(description = "菜单所属父节点ID",requiredMode = RequiredMode.REQUIRED,example = "1")
  private Long parentId;

  @Schema(description = "菜单对应路由地址")
  private String path;

  @Schema(description = "菜单图标")
  private String icon;


  @Schema(description = "菜单状态",example = "ACTIVE")
  @EnumValidate(value = CommonStatusEnum.class,message = "菜单状态不正确")
  @NotNull
  private String status;
}
