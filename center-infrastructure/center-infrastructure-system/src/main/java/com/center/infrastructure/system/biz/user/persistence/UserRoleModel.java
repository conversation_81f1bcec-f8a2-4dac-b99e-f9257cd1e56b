package com.center.infrastructure.system.biz.user.persistence;

import com.center.framework.db.core.BaseModel;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Data;

@Data
@Table(name = "center_system_user_role")
@Entity
public class UserRoleModel extends BaseModel {

  @Column(name = "user_id",nullable = false)
  private Long userId;

  @Column(name = "role_id",nullable = false)
  private Long roleId;
}
