package com.center.infrastructure.system.biz.role;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.role.pojo.*;
import com.center.infrastructure.system.biz.role.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "系统基础功能-角色管理")
@RestController
@RequestMapping("/system/role")
@Validated
public class RoleController {

  private static final Logger log = LoggerFactory.getLogger(RoleController.class);
  @Resource
  private RoleService roleService;

  @GetMapping("/get")
  @Operation(summary = "根据角色ID获取角色详细信息")
  @Parameter(name = "id",description = "角色ID",example = "1")
  public CommonResult<RoleResp> get(@Valid @RequestParam(name = "id")Long id){
    log.info("根据角色ID获取角色详细信息，id:{}",id);
    return CommonResult.success(roleService.get(id));
  }

  @GetMapping("/get_role_list")
  @Operation(summary = "获取角色列表")
  @EnumConvertPoint
  public CommonResult<PageResult<RolePageView>> getRoleList(@Valid RolePageReq rolePageReq){
    log.info("获取角色列表，请求参数:{}",rolePageReq);
    return CommonResult.success(roleService.getRoleList(rolePageReq));
  }

  @PostMapping("/create/{roleName}")
  @Operation(summary = "创建一个新的角色")
  @Parameter(description = "新的角色详细信息")
  public CommonResult<String> save(@Valid @PathVariable String roleName){
    log.info("创建一个新的角色，请求信息:{}",roleName);
    roleService.save(roleName);
    return CommonResult.successWithMessageOnly("创建角色成功");
  }

  @PostMapping("/update")
  @Operation(summary = "更新角色信息",description = "")
  @Parameter(description = "角色详细信息")
  public CommonResult<String> update(@RequestBody @Valid RoleUpdateReq roleUpdateReq){
    log.info("更新角色信息，请求信息:{}",roleUpdateReq);
    roleService.update(roleUpdateReq);
    return CommonResult.successWithMessageOnly("更新角色成功");
  }

  @PostMapping("/delete/{id}")
  @Operation(summary = "删除角色")
  @Parameter(description = "角色ID")
  public CommonResult<String> delete(@Valid @PathVariable Long id){
    log.info("删除角色，id:{}",id);
    roleService.delete(id);
    return CommonResult.successWithMessageOnly("删除角色成功");
  }

  @PostMapping("/active/{id}")
  @Operation(summary = "启用角色")
  public CommonResult<String> active(@PathVariable @Valid Long id) {
    log.info("启用角色，id:{}",id);
    roleService.updateStatus(id, CommonStatusEnum.ACTIVE);
    return CommonResult.successWithMessageOnly("角色启用成功");
  }

  @PostMapping("/inactive/{id}")
  @Operation(summary = "停用角色")
  public CommonResult<String> inactive(@PathVariable @Valid Long id) {
    log.info("停用角色，id:{}",id);
    roleService.updateStatus(id,CommonStatusEnum.INACTIVE);
    return CommonResult.successWithMessageOnly("角色停用成功");
  }
}
