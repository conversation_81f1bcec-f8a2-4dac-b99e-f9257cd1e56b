package com.center.infrastructure.system.biz.depart.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 部门基础类
 */
@Data
public class DepartBase {

    @NotNull
    @Schema(description = "部门名称")
    private String departName;

    @NotNull
    @Schema(description = "上级部门ID")
    private Long parentId;

    @Schema(description = "显示顺序")
    private Integer sort;

    @NotNull
    @Schema(description = "租户ID")
    private Long tenantId;
}
