package com.center.infrastructure.system.biz.role.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoleMenuRepository extends JpaRepository<RoleMenuModel,Long>,
    QuerydslPredicateExecutor<RoleMenuModel> {

  void deleteByRoleId(Long roleId);

  Long countByMenuId(Long menuId);

  List<RoleMenuModel> findByRoleId(Long roleId);
}
