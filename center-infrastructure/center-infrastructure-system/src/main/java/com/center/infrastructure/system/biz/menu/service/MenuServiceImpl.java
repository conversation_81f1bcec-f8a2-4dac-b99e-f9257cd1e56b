package com.center.infrastructure.system.biz.menu.service;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import com.center.framework.common.CommonConstants;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.Tree.TreeNodeUtils;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.infrastructure.system.biz.menu.enumerate.MenuCategoryEnum;
import com.center.infrastructure.system.biz.menu.persistence.MenuModel;
import com.center.infrastructure.system.biz.menu.persistence.MenuRepository;
import com.center.infrastructure.system.biz.menu.pojo.MenuCreateReq;
import com.center.infrastructure.system.biz.menu.pojo.MenuResp;
import com.center.infrastructure.system.biz.menu.pojo.MenuUpdateReq;
import com.center.infrastructure.system.biz.role.persistence.RoleMenuRepository;
import com.center.infrastructure.system.common.ErrorCodeConstant;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class MenuServiceImpl implements MenuService {

  @Resource private MenuRepository menuRepository;

  @Resource private RoleMenuRepository roleMenuRepository;

  @Override
  public void save(MenuCreateReq menuCreateReq) {
    checkParent(menuCreateReq.getParentId());
    checkMenuName(menuCreateReq.getMenuName(), menuCreateReq.getParentId());
    menuRepository.save(OrikaUtils.convert(menuCreateReq, MenuModel.class));
  }

  @Override
  public void update(MenuUpdateReq menuUpdateReq) {
    checkParent(menuUpdateReq.getParentId());
    if (!menuRepository
        .findById(menuUpdateReq.getId())
        .orElseThrow(
            () ->
                ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "菜单不存在"))
        .getMenuName()
        .equals(menuUpdateReq.getMenuName())) {
      checkMenuName(menuUpdateReq.getMenuName(), menuUpdateReq.getParentId());
    }
    getMenuModel(menuUpdateReq.getId());
    menuRepository.save(OrikaUtils.convert(menuUpdateReq, MenuModel.class));
  }

  @Override
  public void delete(Long id) {
    MenuModel menuModel = getMenuModel(id);
    if (menuRepository.countByParentId(menuModel.getId()) > 0) {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "请先删除此菜单下的所有菜单");
    }
    if (roleMenuRepository.countByMenuId(id) > 0) {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "请先删除此菜单分配给的用户");
    }
    menuRepository.deleteById(id);
  }

  @Override
  public MenuResp get(Long id) {
    return OrikaUtils.convert(getMenuModel(id), MenuResp.class);
  }

  @Override
  public List<Tree<String>> getAllMenu() {
    List<MenuModel> menuModelList = menuRepository.findAll();
    return getMenuTree(menuModelList);
  }

  @Override
  public List<MenuResp> getMenuList() {
    List<MenuModel> menuModelList = menuRepository.findAll();
    Long rootId = menuRepository.findByMenuName("知识治理").getId();
    return menuModelList.stream()
        .filter(menuModel -> menuModel.getParentId().equals(rootId))
        .map(menuModel -> OrikaUtils.convert(menuModel, MenuResp.class))
        .collect(Collectors.toList());
  }

  private MenuModel getMenuModel(Long id) {
    Optional<MenuModel> optionalMenuModel = menuRepository.findById(id);
    if (optionalMenuModel.isPresent()) {
      return optionalMenuModel.get();
    } else {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "菜单不存在");
    }
  }

  private void checkParent(Long parentId) {
    if (Objects.equals(parentId, CommonConstants.OBJECT_ROOT_ID)) {
      return;
    }
    Optional<MenuModel> optionalMenuModel = menuRepository.findById(parentId);
    if (!optionalMenuModel.isPresent()) {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "菜单父节点");
    } else if (optionalMenuModel.get().getCategory().equals(MenuCategoryEnum.BUTTON)) {
      throw ServiceExceptionUtil.exception(ErrorCodeConstant.MENU_NOT_BELONG_TO_BUTTON);
    }
  }

  private void checkMenuName(String name, Long parentId) {
    List<MenuModel> menuList = menuRepository.findByParentId(parentId);
    for (MenuModel menuModel : menuList) {
      if (menuModel.getMenuName().equals(name)) {
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "菜单名称重复");
      }
    }
  }

  /**
   * 生成树型结构的菜单树
   * @param menuModelList
   * @return
   */
  private List<Tree<String>> getMenuTree(List<MenuModel> menuModelList) {
    List<TreeNode> nodeList = CollUtil.newArrayList();
    Iterator<MenuModel> iterator = menuModelList.iterator();
    while (iterator.hasNext()) {
      MenuModel menuModel = iterator.next();
      TreeNode<String> treeNode =
          new TreeNode(
              menuModel.getId(),
              menuModel.getParentId(),
              menuModel.getMenuName(),
              menuModel.getSort());
      // 如果还需要给树形添加其他字段，返回给前端，需使用map进行封装
      HashMap<String, Object> hashMap = new HashMap<>();
      hashMap.put("path", menuModel.getPath());
      hashMap.put("category", menuModel.getCategory());
      hashMap.put("icon", menuModel.getIcon());
      hashMap.put("sort", menuModel.getSort());
      treeNode.setExtra(hashMap);
      nodeList.add(treeNode);
    }
    return TreeNodeUtils.buildTree(nodeList);
  }
}
