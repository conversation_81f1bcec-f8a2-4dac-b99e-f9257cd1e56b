package com.center.infrastructure.system.biz.role.persistence;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.infrastructure.system.biz.role.enumerate.RoleEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoleRepository extends JpaRepository<RoleModel, Long>, QuerydslPredicateExecutor<RoleModel> {

    RoleModel findByRoleNameAndCodeAndTenantId(String roleName, RoleEnum code, Long tenantId);

    RoleModel findByRoleName(String name);

    RoleModel findByCode(RoleEnum code);

    List<RoleModel> findByTenantIdAndStatus(Long currentTenantId, CommonStatusEnum status);

    Integer deleteAllByTenantId(Long id);
}
