package com.center.infrastructure.system.biz.role.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RoleUpdateReq  {

  @Schema(description = "角色ID",example = "1")
  @NotNull(message = "角色ID不能为空")
  private Long id;

  @Schema(description = "角色名称",example = "管理员")
  @NotNull(message = "角色名称不能为空")
  private String roleName;
}
