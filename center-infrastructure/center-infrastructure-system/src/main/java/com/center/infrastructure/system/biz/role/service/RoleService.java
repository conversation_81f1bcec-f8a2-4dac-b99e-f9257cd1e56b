package com.center.infrastructure.system.biz.role.service;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.role.pojo.*;

public interface RoleService {

  /**
   * 创建新角色
   * @param roleName-角色信息
   */
  void save(String roleName);

  /**
   * 更新角色信息
   * @param roleUpdateReq-角色更新后信息
   */
  void update(RoleUpdateReq roleUpdateReq);

  /**
   * 获取角色信息
   * @param id-角色id
   * @return 角色信息
   */
  RoleResp get(Long id);

  /**
   * 删除角色
   * @param id-角色id
   */

  void delete(Long id);

  /**
   * 更新状态
   * @param id-角色id
   * @param commonStatusEnum-状态(ACTIVE 启用---INACTIVE 停用)
   */
  void updateStatus(Long id, CommonStatusEnum commonStatusEnum);

  /**
   * 获取角色列表
   * @param rolePageReq-分页条件查询信息
   * @return 角色列表
   */
  PageResult<RolePageView> getRoleList(RolePageReq rolePageReq);
}
