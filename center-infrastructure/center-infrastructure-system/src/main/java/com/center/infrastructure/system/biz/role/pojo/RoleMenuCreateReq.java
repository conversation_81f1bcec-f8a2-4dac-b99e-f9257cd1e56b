package com.center.infrastructure.system.biz.role.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/10/22 17:06
 */
@Data
public class RoleMenuCreateReq {
    @Schema(description = "角色ID",requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @Schema(description = "菜单ID",requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    @NotNull(message = "菜单ID不能为空")
    private Long menuId;
}
