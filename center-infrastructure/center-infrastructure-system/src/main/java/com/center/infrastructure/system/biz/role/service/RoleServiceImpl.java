package com.center.infrastructure.system.biz.role.service;

import cn.hutool.core.util.StrUtil;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageResult;
import com.center.framework.web.pojo.SortProperties;
import com.center.infrastructure.system.biz.menu.persistence.MenuRepository;
import com.center.infrastructure.system.biz.role.enumerate.RoleEnum;
import com.center.infrastructure.system.biz.role.persistence.*;
import com.center.infrastructure.system.biz.role.pojo.*;
import com.center.infrastructure.system.biz.user.persistence.UserRoleModel;
import com.center.infrastructure.system.biz.user.persistence.UserRoleRepository;
import com.center.infrastructure.system.biz.user.service.UserService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class RoleServiceImpl implements RoleService {

  @Resource
  private RoleRepository roleRepository;

  @Resource
  private RoleMenuRepository roleMenuRepository;

  @Resource
  private UserRoleRepository userRoleRepository;

  @Resource
  private MenuRepository menuRepository;

  @Resource
  private JPAQueryFactory queryFactory;

  @Resource
  private UserService userService;

  @Value("角色管理")
  private String rolePermission;

  @Value("SUPERADMIN")
  private String superAdmin;

  @Value("ADMIN")
  private String admin;

  @Override
  @Transactional
  public void save(String roleName) {
      checkNameAndCodeAndTenantId(roleName, RoleEnum.EMPLOYEE, LoginContextHolder.getLoginUserTenantId());
      RoleModel roleModel=new RoleModel();
      roleModel.setRoleName(roleName);
      roleModel.setSort(0);
      roleModel.setCode(RoleEnum.EMPLOYEE);
      roleModel.setStatus(CommonStatusEnum.ACTIVE);
      roleRepository.save(roleModel);
      log.info("角色基础信息保存成功");
//      List<RoleMenuModel> listRoleMenu = new LinkedList<>();
//      Iterator<Long> it = roleCreateReq.getListMenuId().iterator();
//      while (it.hasNext()) {
//        Long id = it.next();
//        checkMenuId(id);
//        RoleMenuModel roleMenuModel = new RoleMenuModel();
//        roleMenuModel.setMenuId(id);
//        roleMenuModel.setRoleId(roleModel.getId());
//        listRoleMenu.add(roleMenuModel);
//      }
//      roleMenuRepository.saveAll(listRoleMenu);
//      log.info("角色权限信息保存成功");
  }

  @Override
  public void update(RoleUpdateReq roleUpdateReq) {
      checkName(roleUpdateReq.getRoleName(), roleUpdateReq.getId());
      roleRepository.save(OrikaUtils.convert(roleUpdateReq, RoleModel.class));
  }

  @Override
  public void updateStatus(Long id, CommonStatusEnum commonStatusEnum) {
      RoleModel roleModel = roleRepository.findById(id).orElseThrow(() ->
              ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此角色不存在"));
      if (admin.equals(roleModel.getCode().getValue())||superAdmin.equals(roleModel.getCode().getValue())) {
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "管理员不能改变状态");
      }
      if(userRoleRepository.countByRoleId(id) > 0){
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "该角色下有用户，不能停用");
      }
      if (!roleModel.getStatus().equals(commonStatusEnum)) {
        roleModel.setStatus(commonStatusEnum);
        roleRepository.save(roleModel);
      } else throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "用户状态错误");
  }

  @Override
  public RoleResp get(Long id) {
    return OrikaUtils.convert(getRoleModel(id),RoleResp.class);
  }

  @Override
  public PageResult<RolePageView> getRoleList(RolePageReq req) {
    try {
      Long currentTenantId = LoginContextHolder.getLoginUserTenantId();
      Pageable pageable = PageRequest.of(req.getPageNo() - 1
              , req.getPageSize()
              , Sort.by(Sort.Direction.DESC,
                      SortProperties.CREATE_TIME));
      String content = req.getContent();
      QRoleModel qRoleModel=QRoleModel.roleModel;
      BooleanBuilder builder = new BooleanBuilder();
      if (StrUtil.isNotEmpty(content)) {
        builder.or(qRoleModel.roleName.contains(content));
      }
      builder.and(qRoleModel.tenantId.eq(currentTenantId));
      JPQLQuery<RolePageView> jpqlQuery = queryFactory.select((Projections.bean(
                      RolePageView.class,
                      qRoleModel.id,
                      qRoleModel.roleName,
                      qRoleModel.status,
                      qRoleModel.code
              )))
              .from(qRoleModel)
              .orderBy(qRoleModel.sort.desc(),qRoleModel.createTime.desc())
              .offset(pageable.getOffset())
              .limit(pageable.getPageSize())
              .where(builder);
      Long total = jpqlQuery.fetchCount();
      List<RolePageView> list = jpqlQuery.fetch();
      return PageResult.of(list, total);
    } catch (IllegalArgumentException e) {
      log.error("查询参数错误",e );
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR,e,"获取角色列表失败");
    } catch (Exception e) {
      log.error("获取列表失败", e);
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR,e,"获取角色列表失败");
    }

  }

  @Override
  public void delete(Long id) {
      try {
  //      删除前需要检查此角色下是否有用户
        if(userRoleRepository.countByRoleId(id)>0){
          throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DELETE_OBJECT_ERROR,"存在该角色用户，请调整后停用");
        }
        RoleModel roleModel = roleRepository.findById(id).orElseThrow(()
                -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "角色不存在"));
        if(!roleModel.getCode().equals(RoleEnum.ADMIN) && !roleModel.getCode().equals(RoleEnum.SUPERADMIN)) {
          roleRepository.deleteById(id);
        }else
          throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN,"不能删除超级管理员/管理员");
      }catch (EmptyResultDataAccessException e){
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,e,"角色不存在");
      }
  }

  /**
   * 检查权限是否存在
   * @param id-权限id
   */
  private void checkMenuId(Long id) {
    menuRepository.findById(id).orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"菜单不存在"));
  }

  private RoleModel getRoleModel(Long id){
    Optional<RoleModel> optionalRoleModel = roleRepository.findById(id);
    if(optionalRoleModel.isPresent()){
      return optionalRoleModel.get();
    }else {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"角色不存在");
    }
  }

  /**
   * 更新角色时检查角色名称和编码是否重复
   */
  private void checkName(String name,Long id){
    RoleModel roleModel = getRoleModel(id);
    if(!name.equals(roleModel.getRoleName())){
      roleModel = roleRepository.findByRoleName(name);
      if(null != roleModel){
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT,"重复的角色名称");
      }
    }
  }
  /**
   * 创建角色时检查角色名称和编码是否重复
   */
  private void checkNameAndCodeAndTenantId(String name, RoleEnum code, Long tenantId){
    try {
      RoleModel roleModel = roleRepository.findByRoleNameAndCodeAndTenantId(name, code, tenantId);
      if (null != roleModel) {
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的角色名称或代码");
      }
    }catch (Exception e){
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,e,"租户不存在");
    }
  }
}
