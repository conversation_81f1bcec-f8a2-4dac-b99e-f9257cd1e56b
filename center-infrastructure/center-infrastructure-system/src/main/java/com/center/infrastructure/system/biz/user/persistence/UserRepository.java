package com.center.infrastructure.system.biz.user.persistence;

import com.center.framework.common.enumerate.CommonStatusEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserRepository extends JpaRepository<UserModel, Long>,
    QuerydslPredicateExecutor<UserModel> {

  UserModel findByUsernameAndTenantId(String userName, Long TenantId);

  Long countByTenantIdAndStatus(Long tenantId, CommonStatusEnum status);

  Long countByTenantId(Long tenantId);

  UserModel findByUsernameAndPassword(String username,String password);

  List<UserModel> findByUpdaterIdIn(List<Long> updaterIdsList);

  UserModel findByPhoneNumber(String phoneNumber);

  Integer deleteAllByTenantId(Long id);

  boolean existsByDepartId(Long id);

    UserModel findByPhoneNumberAndPassword(String phoneNumber, String password);

    Integer countByUsername(String username);

    boolean existsByUsername(String managerName);

  boolean existsByUsernameAndTenantId(String username, Long tenantId);
}
