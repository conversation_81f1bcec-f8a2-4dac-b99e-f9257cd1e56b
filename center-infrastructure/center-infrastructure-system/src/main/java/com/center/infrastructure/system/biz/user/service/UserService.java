package com.center.infrastructure.system.biz.user.service;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.depart.pojo.DepartResp;
import com.center.infrastructure.system.biz.role.pojo.RoleResp;
import com.center.infrastructure.system.biz.user.pojo.*;

import java.util.List;

public interface UserService {

  /**
   * 获取指定用户信息
   * @param id-用户id
   */
  UserResp get(Long id);
  /**
   * 获取用户列表信息
   * @param userPageReq-分页请求
   */
  PageResult<UserPageView> page(UserPageReq userPageReq);
  /**
   * 创建新用户，关联所属公司、部门和角色
   * @param userCreateReq-创建请求
   */
  void save(UserCreateReq userCreateReq);
  /**
   * 更新用户信息
   * @param userUpdateReq-更新请求
   */
  void update(UserUpdateReq userUpdateReq);
  /**
   * 删除用户
   * @param id-用户id
   */
  void delete(Long id);
  /**
   * 根据用户名和密码获取用户信息
   * @param username-用户名
   * @param password-密码
   */
  UserResp getByUsernameAndPassword(String username,String password);

  UserResp getByPhoneNumberAndPassword(String phoneNumber,String password);
  /**
   * 更新用户登录信息
   * @param id-用户id
   * @param ip-登录ip
   */
  void updateUserLoginInfo(Long id,String ip);
  /**
   * 重置密码
   * @param id-用户id
   */
  void resetPassword(Long id);
  /**
   * 更新用户状态
   * @param id-用户id
   * @param commonStatusEnum-状态
   */
  void updateStatus(Long id, CommonStatusEnum commonStatusEnum);
  /**
   * 获取角色列表
   */
  List<RoleResp> getRole();

  /**
   * 获取部门列表
   */
  List<DepartResp> getDepart();

  /**
   * 判断是否是超级管理员
   * @param userId-用户id
   */
  Boolean isSuperAdmin(Long userId);

  /**
   * 判断是否是管理员
   * @param userId-用户id
   */
  Boolean isAdmin(Long userId);

  /**
   * 保存登录信息
   */
  void saveLoginInfo();

  /**
   * 设置密码
   * @param password-密码
   */
  void setPassword(PasswordReq passwordReq);
}
