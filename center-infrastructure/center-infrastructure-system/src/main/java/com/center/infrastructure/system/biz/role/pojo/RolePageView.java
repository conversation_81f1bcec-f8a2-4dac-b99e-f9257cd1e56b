package com.center.infrastructure.system.biz.role.pojo;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.center.framework.web.pojo.PageParam;
import com.center.infrastructure.system.biz.role.enumerate.RoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RolePageView {
    @Schema(description = "角色id")
    private Long id;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色状态")
    private CommonStatusEnum status;

    @Schema(description = "角色状态名称")
    @EnumConvert(value = CommonStatusEnum.class, srcFieldName = "status")
    private String statusName;

    @Schema(description = "角色code")
    private RoleEnum code;

    @Schema(description = "角色code名称")
    @EnumConvert(value = RoleEnum.class, srcFieldName = "code")
    private String codeName;
}