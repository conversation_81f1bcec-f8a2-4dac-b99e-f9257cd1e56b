package com.center.infrastructure.system.biz.user.pojo;

import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.center.infrastructure.system.biz.role.enumerate.RoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserPageView {
    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "显示名")
    private String displayName;

    @Schema(description = "手机号")
    private String phoneNumber;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "状态")
    private CommonStatusEnum status;

    @Schema(description = "状态名")
    @EnumConvert(value = CommonStatusEnum.class,srcFieldName = "status")
    private String statusName;

    @Schema(description = "部门名称")
    private String departName;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色code")
    private RoleEnum code;

    @Schema(description = "角色code名称")
    @EnumConvert(value = RoleEnum.class, srcFieldName = "code")
    private String codeName;
}
