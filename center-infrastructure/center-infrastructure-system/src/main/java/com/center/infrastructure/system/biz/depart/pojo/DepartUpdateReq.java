package com.center.infrastructure.system.biz.depart.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class DepartUpdateReq{
    @NotNull(message = "部门ID不能为空")
    @Schema(description = "部门ID")
    private Long id;

    @Schema(description = "部门名称")
    @NotNull(message = "部门名称不能为空")
    private String name;
}

