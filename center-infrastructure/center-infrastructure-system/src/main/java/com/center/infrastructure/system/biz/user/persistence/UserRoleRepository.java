package com.center.infrastructure.system.biz.user.persistence;

import com.center.infrastructure.system.biz.user.pojo.UserRoleUpdateReq;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRoleRepository extends JpaRepository<UserRoleModel,Long>,
    QuerydslPredicateExecutor<UserRoleModel> {

  Long countByRoleId(Long roleId);

  UserRoleModel findByUserId(Long id);

  UserRoleModel findByRoleId(Long id);
}
