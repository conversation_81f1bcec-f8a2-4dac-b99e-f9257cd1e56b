package com.center.infrastructure.system.biz.user.pojo;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.center.infrastructure.system.biz.role.enumerate.RoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserInfo {
    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "昵称")
    private String displayName;

    @Schema(description = "手机号")
    private String phoneNumber;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "是否假删除")
    private Boolean isDeleted;

    @Schema(description = "角色id")
    private Long roleId;

    @Schema(description = "角色代码")
    private RoleEnum code;

    @EnumConvert(value = RoleEnum.class,srcFieldName = "code")
    @Schema(description = "角色名",example = "管理员")
    private String roleName;

    @Schema(description = "角色状态")
    private CommonStatusEnum roleStatus;

    @EnumConvert(value = CommonStatusEnum.class,srcFieldName = "roleStatus")
    @Schema(description = "角色状态名",example = "启用")
    private String roleStatusName;

    @Schema(description = "角色备注")
    private String remark;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "租户介绍")
    private String tenantDescription;

    @Schema(description = "租户状态")
    private CommonStatusEnum tenantStatus;

    @EnumConvert(value = CommonStatusEnum.class,srcFieldName = "tenantStatus")
    @Schema(description = "租户状态名",example = "启用")
    private String tenantStatusName;

    @Schema(description = "部门id")
    private Long departId;

    @Schema(description = "部门名称")
    private String departName;
}
