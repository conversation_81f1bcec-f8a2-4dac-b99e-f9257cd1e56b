package com.center.infrastructure.system.biz.user.pojo;

import cn.hutool.crypto.SecureUtil;
import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public abstract class BaseUser {

  @Schema(description = "用户名",requiredMode = RequiredMode.REQUIRED,example = "admin")
  @Pattern(regexp = "^[a-zA-Z0-9]{2,20}$", message = "用户名由数字或字母组成，长度为 2-20 位")
  private String username;

  @Schema(description = "密码",defaultValue ="123456")
  @Length(min = 6, max = 10, message = "密码长度为6-10位")
  private String password;

  @Schema(description = "显示名",example = "管理员")
  @Length(max = 20,message = "显示名称长度为20个字符以内")
  private String displayName;

  @Schema(description = "用户状态",example = "ACTIVE",defaultValue = "ACTIVE")
  @EnumValidate(message = "用户状态不正确",value = CommonStatusEnum.class)
  private String status;

  @Schema(description = "状态名")
  @EnumConvert(value = CommonStatusEnum.class,srcFieldName = "status")
  private String statusName;

  @Schema(description = "租户ID",example = "1")
  private Long tenantId;

  @Schema(description = "部门ID",requiredMode = RequiredMode.REQUIRED,example = "1")
  @NotNull(message = "部门ID不能为空")
  private Long departId;

  @Schema(description = "邮箱",example = "<EMAIL>")
  @Email(message = "邮箱格式不正确")
  private String email;

  @Schema(description = "手机号",requiredMode = Schema.RequiredMode.REQUIRED,example = "123456789")
  @NotNull(message = "手机号不能为空")
  @Pattern(regexp = "^\\d{11}$", message = "手机号必须为11位数字")
  private String phoneNumber;

  public String getMD5Password(){
    return SecureUtil.md5(getPassword());
  }
}
