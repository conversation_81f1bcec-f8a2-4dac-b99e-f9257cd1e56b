package com.center.infrastructure.system.biz.menu;

import cn.hutool.core.lang.tree.Tree;
import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.menu.pojo.MenuCreateReq;
import com.center.infrastructure.system.biz.menu.pojo.MenuResp;
import com.center.infrastructure.system.biz.menu.pojo.MenuUpdateReq;
import com.center.infrastructure.system.biz.menu.service.MenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "系统基础功能-权限管理")
@RequestMapping("/system/menu")
@Validated
@RestController
@Slf4j
public class MenuController {

  @Resource
  private MenuService menuService;

  @GetMapping("/get")
  @Operation(summary = "根据权限ID获取权限信息")
  @Parameter(name = "id",description = "权限ID",example = "1")
  public CommonResult<MenuResp> get(@RequestParam(name = "id") Long id){
    log.info("根据权限ID获取权限信息,id:{}",id);
    return CommonResult.success(menuService.get(id));
  }

  @PostMapping("/create")
  @Operation(summary = "创建权限")
  @Parameter(description = "权限详细信息")
  public CommonResult<String> create(@RequestBody @Valid MenuCreateReq menuCreateReq){
    log.info("创建权限,请求:{}",menuCreateReq);
    menuService.save(menuCreateReq);
    return CommonResult.successWithMessageOnly("创建权限成功");
  }

  @PostMapping("/update")
  @Operation(summary = "修改权限信息")
  @Parameter(description = "权限详细信息")
  public CommonResult<String> update(@RequestBody @Valid MenuUpdateReq menuUpdateReq){
    log.info("修改权限信息,请求:{}",menuUpdateReq);
    menuService.update(menuUpdateReq);
    return CommonResult.successWithMessageOnly("修改权限信息成功");
  }

  @PostMapping("/delete")
  @Operation(summary = "根据权限ID删除权限")
  @Parameter(name = "id",description = "权限ID",example = "1")
  public CommonResult<String> delete(@RequestParam(name = "id") Long id){
    log.info("根据权限ID删除权限,id:{}",id);
    menuService.delete(id);
    return CommonResult.successWithMessageOnly("删除权限成功");
  }

  @GetMapping("/get_all_menu")
  @Operation(summary = "查询权限树")
  public CommonResult<List<Tree<String>>> getAllMenu(){
    log.info("查询权限树");
    return CommonResult.success(menuService.getAllMenu());
  }

  @GetMapping("/get_menu_list")
  @Operation(summary = "查询权限列表")
  public CommonResult<List<MenuResp>> getMenuList(){
    log.info("查询权限列表");
    return CommonResult.success(menuService.getMenuList());
  }
}
