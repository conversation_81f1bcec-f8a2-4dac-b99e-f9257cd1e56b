package com.center.infrastructure.system.biz.role.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class RoleCreateReq extends BaseRole{
    @Schema(description = "菜单ID列表",requiredMode = Schema.RequiredMode.REQUIRED,example = "[1,2,3]")
    @NotNull(message = "菜单ID列表不能为空")
    private List<Long> listMenuId;
}
