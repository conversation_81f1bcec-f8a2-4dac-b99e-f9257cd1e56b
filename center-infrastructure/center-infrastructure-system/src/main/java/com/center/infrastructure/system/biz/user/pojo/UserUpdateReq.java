package com.center.infrastructure.system.biz.user.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UserUpdateReq extends BaseUser {

  @Schema(description = "用户ID",example = "1")
  @NotNull(message = "角色ID不能为空")
  private Long id;

  @Schema(description = "角色ID",requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
  @NotNull(message = "角色ID不能为空")
  private Long roleId;
}
