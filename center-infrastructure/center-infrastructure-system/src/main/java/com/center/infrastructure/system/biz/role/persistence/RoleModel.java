package com.center.infrastructure.system.biz.role.persistence;


import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseModel;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

import com.center.framework.db.core.BaseTenantModel;
import com.center.infrastructure.system.biz.role.enumerate.RoleEnum;
import lombok.Data;

@Table(name = "center_system_role")
@Entity
@Data
public class RoleModel extends BaseTenantModel {

  @Column(name = "name" ,nullable = false)
  private String roleName;

  @Column(name = "code" ,nullable = false)
  @Enumerated(EnumType.STRING)
  private RoleEnum code;

  @Column(name = "sort" ,nullable = false)
  private Integer sort;

  @Column(name = "status" ,nullable = false)
  @Enumerated(EnumType.STRING)
  private CommonStatusEnum status;

  @Column(name = "remark")
  private String remark;

}
