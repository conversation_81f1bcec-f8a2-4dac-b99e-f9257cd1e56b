package com.center.infrastructure.system.biz.role.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RoleMenuReq {

  @Schema(description = "角色ID",requiredMode = RequiredMode.REQUIRED,example = "1")
  @NotNull(message = "角色ID不能为空")
  private Long roleId;

  @Schema(description = "菜单ID列表",requiredMode = RequiredMode.REQUIRED,example = "[1,2,3]")
  @NotNull(message = "菜单ID列表不能为空")
  private List<Long> listMenuId;
}
