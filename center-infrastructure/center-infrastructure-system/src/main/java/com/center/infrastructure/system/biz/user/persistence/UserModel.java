package com.center.infrastructure.system.biz.user.persistence;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseDeleteModel;
import com.center.framework.db.core.BaseTenantModel;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.Where;

@Table(name = "center_system_user")
@Entity
@Data
@Where(clause = "is_deleted = 0")
public class UserModel extends BaseDeleteModel {

  @Column(name = "username",nullable = false)
  private String username;

  @Column(name = "password",nullable = false)
  private String password;

  @Column(name = "display_name")
  private String displayName;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private CommonStatusEnum status;

  @Column(name = "login_ip")
  private String loginIp;

  @Column(name = "login_time")
  private LocalDateTime loginTime;

  @Column(name = "depart_id")
  private Long departId;

  @Column(name = "email")
  private String email;

  @Column(name = "phone_number")
  private String phoneNumber;
}
