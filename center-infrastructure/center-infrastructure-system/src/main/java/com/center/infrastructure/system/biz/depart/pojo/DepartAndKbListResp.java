package com.center.infrastructure.system.biz.depart.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/10/16 16:18
 */
@Data
@AllArgsConstructor
public class DepartAndKbListResp {

    @Schema(description = "部门ID")
    private Long id;

    @Schema(description = "部门/知识库 名称")
    private String name;

    @Schema(description = "部门路径")
    private String path;

    @Schema(description = "是否部门")
    private Boolean isDepart;

    private List<DepartAndKbListResp> childDepartRespList;
}
