package com.center.infrastructure.system.biz.menu.service;

import cn.hutool.core.lang.tree.Tree;
import com.center.infrastructure.system.biz.menu.pojo.MenuCreateReq;
import com.center.infrastructure.system.biz.menu.pojo.MenuResp;
import com.center.infrastructure.system.biz.menu.pojo.MenuUpdateReq;
import java.util.List;

public interface MenuService {
  /**
   * 创建权限
   * @param menuCreateReq-权限信息
   */
  void save(MenuCreateReq menuCreateReq);
  /**
   * 修改权限
   * @param menuUpdateReq-权限信息
   */
  void update(MenuUpdateReq menuUpdateReq);
  /**
   * 删除权限
   * @param id-权限ID
   */
  void delete(Long id);
  /**
   * 根据权限ID获取权限信息
   * @param id-权限ID
   */
  MenuResp get(Long id);
  /**
   * 查询权限树
   * @return 权限树
   */
  List<Tree<String>> getAllMenu();
  /**
   * 查询权限列表
   * @return 权限列表
   */
  List<MenuResp> getMenuList();

}
