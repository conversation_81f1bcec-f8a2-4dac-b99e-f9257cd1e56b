package com.center.framework.api.maven;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.center.framework.api.maven.config.MavenProperties;
import com.center.framework.web.pojo.CommonResult;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Tag(name = "获取maven打包时间")
@RestController
@RequestMapping("/maven")
public class MavenController {
    @Resource
    private MavenProperties mavenProperties;

    @GetMapping("/package_time")
    @Operation(summary = "获取maven打包时间")
    public CommonResult<String> getMavenPackageTime()  {
        return CommonResult.success("最新打包时间:"+modifyTime(mavenProperties.getPackage_time()));
    }

    /**
     * 修改时间为东8区
     */
    public String modifyTime(String date) {
        DateTime dateTime = DateUtil.parse(date);
        return DateUtil.offsetHour(dateTime,8).toString();
    }
}
