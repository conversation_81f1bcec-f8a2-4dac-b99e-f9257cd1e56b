package com.center.framework.api.maven.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

@Configuration
@ConfigurationProperties(prefix = "maven", ignoreUnknownFields = false)
@PropertySource(value= "classpath:config/maven.properties",encoding = "utf-8")
@Data
@Component
public class MavenProperties {
    /**maven打包时间*/
    private String package_time;
}