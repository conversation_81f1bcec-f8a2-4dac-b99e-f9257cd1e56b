package com.center.framework.storage.hdfs.impl;



import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.pdf.component.PDFConvert;
import com.center.framework.storage.interfaces.FileStorageService;
import com.center.framework.storage.interfaces.enums.FileCategoryEnum;
import com.center.framework.storage.interfaces.pojo.FileListResp;
import com.center.framework.storage.interfaces.pojo.FileMetadata;
import com.center.framework.storage.interfaces.template.FileTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.fs.*;
import org.apache.hadoop.io.IOUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

/**
 * 基于 HDFS 的文件存储服务实现。
 */
@Service
@Slf4j
@ConditionalOnProperty(name = "storage.type", havingValue = "HDFS")
public class HdfsFileStorageService implements FileStorageService {


    @Value("${hadoop.base.url}")
    private String baseUrl;

    @Value("${hadoop.base.query}")
    private String baseQuery;

    @Autowired
    private FileTemplate fileTemplate;

    @Resource
    private PDFConvert pdfConvert;

    private final FileSystem fileSystem;
    @Value("${hadoop.base-path}")
    private String basePath;
    /**
     * 构造方法，注入 HDFS FileSystem 对象。
     *
     * @param fileSystem HDFS FileSystem 对象
     */
    public HdfsFileStorageService(FileSystem fileSystem) {
        this.fileSystem = fileSystem;
    }

    @Override
    public void deleteDirectory(String rootPath) {
        Path path = new Path(rootPath);
        try {
            // 删除指定路径
            boolean isDeleted = fileSystem.delete(path, true);
            if (!isDeleted) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, "目录未找到或无法删除");
            }
        } catch (IOException e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, "目录删除失败");
        }
    }
    @Override
    public void uploadFile(String sourcePath, String targetPath) {
        try {
            Path hdfsPath = new Path(targetPath);
            // 上传到 HDFS
            Path path = new Path(sourcePath);
            fileSystem.copyFromLocalFile(path, hdfsPath);
        } catch (IOException e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,e, "上传文件到HDFS失败");
        }
    }
    @Override
    public void copyToLocal(String originPath, String tempFilePath) {
        try (InputStream inputStream = fileSystem.open(new Path(originPath))){
            Files.copy(inputStream, Paths.get(tempFilePath), StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, e, "hdfs下载文件时异常");
        }
    }
    @Override
    public List<String> getFileList(String dirPath) {
        List<String> result = new ArrayList<>();
        Path hdfsPath = new Path(dirPath);
        try {
            if (fileSystem.exists(hdfsPath)) {
                FileStatus[] fileStatus = fileSystem.listStatus(hdfsPath);
                for (FileStatus fileStatu : fileStatus) {
                    if (fileStatu.isDirectory()) {
                        result.addAll(getFileList(fileStatu.getPath().toString()));
                    } else {
                        result.add(fileStatu.getPath().toString());
                    }
                }
            }
        } catch (IOException e) {
            log.error("hdfs查询文件时异常", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR, e, "查询文件列表失败");
        }
        return result;
    }
    /**
     * 用于删除文件及其所在的文件夹
     * @param filePaths 文件的路径
     */
    @Override
    public void deleteParentDirectoryOfFilePath(List<String> filePaths) {
        for (String hdfsPath : filePaths) {
            Path path = new Path(hdfsPath);
            try {
                fileSystem.delete(path.getParent(), true);
            } catch (IOException e) {
                log.error("删除文件异常", e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件删除失败");
            }
        }
    }
    @Override
    public void uploadFile(String targetPath, InputStream inputStream,Boolean overwrite) throws IOException {
        Path hdfsPath = buildHdfsPath(targetPath);
        FSDataOutputStream outputStream = null;
        try {
            // 创建父目录（如果不存在）
            if (!fileSystem.exists(hdfsPath.getParent())) {
                boolean mkdirs = fileSystem.mkdirs(hdfsPath.getParent());
                if (!mkdirs) {
                    log.error("创建目录失败：{}", hdfsPath.getParent());
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.PATH_CREATE_ERROR);
                }
            }
            outputStream = fileSystem.create(hdfsPath, overwrite);
            IOUtils.copyBytes(inputStream, outputStream, 4096, false);
            log.info("文件上传成功：{}", hdfsPath);
        } catch (IOException e) {
            log.error("文件上传失败：{}", hdfsPath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e, targetPath);
        } finally {
            IOUtils.closeStream(outputStream);
            IOUtils.closeStream(inputStream);
        }
    }

    @Override
    public void deleteFile(String filePath) throws IOException {
        Path hdfsPath = buildHdfsPath(filePath);
        try {
            if (!fileSystem.exists(hdfsPath)) {
                log.warn("文件不存在，无法删除：{}", hdfsPath);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, hdfsPath);
            }
            boolean result = fileSystem.delete(hdfsPath, false);
            if (result) {
                log.info("文件删除成功：{}", hdfsPath);
            } else {
                log.error("文件删除失败：{}", hdfsPath);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DELETE_OBJECT_ERROR, hdfsPath);
            }
        } catch (IOException e) {
            log.error("删除文件时发生异常：{}", hdfsPath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "删除文件失败");
        }
    }

    @Override
    public InputStream downloadFile(String filePath) throws IOException {
        Path hdfsPath = buildHdfsPath(filePath);
        try {
            if (!fileSystem.exists(hdfsPath)) {
                log.warn("文件不存在，无法下载：{}", hdfsPath);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, hdfsPath);
            }
            log.info("文件下载成功：{}", hdfsPath);
            return fileSystem.open(hdfsPath);
        } catch (IOException e) {
            log.error("文件下载失败：{}", hdfsPath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件下载失败");
        }
    }

    @Override
    public FileMetadata getFileMetadata(String filePath) throws IOException {
        Path hdfsPath = buildHdfsPath(filePath);
        try {
            if (!fileSystem.exists(hdfsPath)) {
                log.warn("文件不存在，无法获取元数据：{}", hdfsPath);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, filePath);
            }
            FileStatus status = fileSystem.getFileStatus(hdfsPath);
            log.info("获取文件元数据成功：{}", hdfsPath);
            return new FileMetadata(
                    hdfsPath.getName(),
                    status.getLen(),
                    status.getModificationTime(),
                    hdfsPath.toString()
            );
        } catch (IOException e) {
            log.error("获取文件元数据失败：{}", hdfsPath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "获取文件元数据失败");
        }
    }

    private Path buildHdfsPath(String relativePath) {
        // 确保 basePath 以 '/' 结尾
        String normalizedBasePath = basePath.endsWith("/") ? basePath : basePath + "/";
        // 移除 relativePath 开头的 '/'
        String normalizedRelativePath = relativePath.startsWith("/") ? relativePath.substring(1) : relativePath;
        normalizedRelativePath = relativePath.startsWith(basePath) ? relativePath.substring(basePath.length()+1) : relativePath;
        // 拼接基础路径和相对路径
        return new Path(normalizedBasePath + normalizedRelativePath);
    }


    @Override
    public ResponseEntity<ByteArrayResource> previewFile(String filePath) throws IOException {
        byte[] content;
        Path path = buildPathForPreview(filePath);
        String url = path + baseQuery; // 将 baseQuery 添加到完整路径后面

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url.toString());

        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
            if (response.getStatusLine().getStatusCode() == 200) {
                content = EntityUtils.toByteArray(response.getEntity());
            } else {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, filePath);
            }
        }catch (Exception e){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件预览失败");
        }

        ByteArrayResource resource = new ByteArrayResource(content);

        //获取相应头
        HttpHeaders headers = fileTemplate.getHeaders(filePath);
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(content.length)
                .body(resource);
    }

    @Override
    public String convertPdf(String originPath) {
        Path hdfsPath = buildHdfsPath(originPath);
        Path path = new Path(originPath);
        String pdfName = hdfsPath.getName();
        File pdfFile = null;
        File crcfile = null;
        InputStream inputStream= null;
        InputStream pdfInputStream = null;
        FileOutputStream outputStream = null;
        if (!pdfName.endsWith(".pdf")) {
            int lastDotIndex = pdfName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                pdfName = pdfName.substring(0, lastDotIndex);
            }
            pdfName = pdfName + ".pdf";
            File localTempFile = null;
            try {
                localTempFile = File.createTempFile("temp", ".odt");
                String tempFilePath = localTempFile.getAbsolutePath();
                // 读取文件并写入到本地临时文件
                inputStream= downloadFile(originPath);
                outputStream = new FileOutputStream(tempFilePath);
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                //获取临时路径名
                Path targetpath = new Path(tempFilePath);
                // 转换为 PDF
                String pdfPath = pdfConvert.toPdf(tempFilePath, targetpath.getParent().toString());
                // 上传文件
                pdfInputStream = new FileInputStream(pdfPath);
                String targetPath = path.getParent().toString() + "/" + pdfName;
                uploadFile(targetPath, pdfInputStream,true);
                pdfFile = new File(pdfPath);
                String crcPath = localTempFile.getParent() + "\\\\." + localTempFile.getName() + ".crc";
                crcfile = new File(crcPath);
                return targetPath;
            } catch (IOException e) {
                log.error("文件上传异常", e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e);
            } finally {
                try {
                    // 关闭文件流
                    if (inputStream != null) {
                        inputStream.close();
                    }
                    if (pdfInputStream != null) {
                        pdfInputStream.close();
                    }
                    if (outputStream != null) {
                        outputStream.close();
                    }
                } catch (IOException e) {
                    log.error("关闭文件流异常", e);
                }
                // 删除临时文件
                if (localTempFile != null && localTempFile.exists()) {
                    localTempFile.delete();
                }
                if (pdfFile != null && pdfFile.exists()) {
                    pdfFile.delete();
                }
                if (crcfile != null && crcfile.exists()) {
                    crcfile.delete();
                }
            }
        }
        return hdfsPath.toString();
    }

    private Path buildPathForPreview(String relativePath) {
        // 确保 basePath 以 '/' 结尾
        String normalizedBasePath = baseUrl.endsWith("/") ? baseUrl : baseUrl + "/";
        // 移除 relativePath 开头的 '/'
        String normalizedRelativePath = relativePath.startsWith("/") ? relativePath.substring(1) : relativePath;
        // 拼接基础路径和相对路径
        return new Path(normalizedBasePath + normalizedRelativePath);
    }
    @Override
    public List<FileListResp> getAllFiles(String path) {
        List<FileListResp> fileListResps = new ArrayList<>();
        try {
            // HDFS 获取文件列表
            FileStatus[] fileStatuses = fileSystem.listStatus(buildHdfsPath(path));
            for (FileStatus fileStatus : fileStatuses) {
                FileListResp fileListResp = new FileListResp();
                fileListResp.setFileName(fileStatus.getPath().getName());
                fileListResp.setFilePath(fileStatus.getPath().toString());
                fileListResp.setFileCategory(fileStatus.isDirectory() ? FileCategoryEnum.DIR : FileCategoryEnum.FILE);
                // 这里设置额外的信息
                fileListResp.setFileSize(fileStatus.getLen());  // 文件大小
                fileListResps.add(fileListResp);
            }
        } catch (IOException e) {
            log.error("Error fetching files from HDFS", e);
        }
        return fileListResps;
    }

    @Override
    public void copyFromCloudToCloud(String filePath, String dfsPath) {
        try (FSDataInputStream inputStream = fileSystem.open(new Path(filePath));
             FSDataOutputStream outputStream = fileSystem.create(new Path(dfsPath), true)){
            IOUtils.copyBytes(inputStream, outputStream, 4096, false);
        }catch (IOException e){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e);
        }
    }
}
