package com.center.framework.storage.hdfs.config;

import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

import java.io.IOException;

/**
 * HDFS 配置类，创建并配置 HDFS FileSystem 对象。
 */
@org.springframework.context.annotation.Configuration
@ConditionalOnProperty(name = "storage.type", havingValue = "HDFS")
public class HDFSConfig {

    @Value("${hadoop.fs.defaultFS}")
    private String defaultFS;

    @Value("${hadoop.user.name}")
    private String hadoopUserName;
    @Value("${hadoop.dfs.client.use-datanode-hostname}")
    private String useDatanodeHostname;
    /**
     * 创建并返回 HDFS FileSystem 对象。
     *
     * @return FileSystem 对象
     * @throws IOException 如果发生 IO 异常
     */
    @Bean
    public FileSystem fileSystem() {
        try {
            System.setProperty("HADOOP_USER_NAME", hadoopUserName);
            Configuration conf = new Configuration();
            conf.set("fs.defaultFS", defaultFS);
            conf.set("dfs.client.use.datanode.hostname", useDatanodeHostname);
            return FileSystem.get(conf);
        } catch (IOException e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "初始化 HDFS FileSystem 失败");
        }
    }
}
