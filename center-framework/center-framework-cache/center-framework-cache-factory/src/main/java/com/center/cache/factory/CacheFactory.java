package com.center.cache.factory;

import com.center.cache.interfaces.Cache;
import com.center.cache.memory.MemoryHashCache;
import com.center.cache.memory.MemoryListCache;
import com.center.cache.memory.MemorySetCache;
import com.center.cache.memory.MemoryStrCache;
import com.center.cache.redis.RedisHashCache;
import com.center.cache.redis.RedisListCache;
import com.center.cache.redis.RedisSetCache;
import com.center.cache.redis.RedisStrCache;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @description: cache工厂类
 * @date 2024/11/12 15:03
 */
@Component
public class CacheFactory {

    @Value("${cache.type}")
    private String cacheType;

    @Resource
    private MemoryHashCache memoryHashCache;
    @Resource
    private MemoryListCache memoryListCache;
    @Resource
    private MemorySetCache memorySetCache;
    @Resource
    private MemoryStrCache memoryStrCache;
    @Resource
    private RedisHashCache redisHashCache;
    @Resource
    private RedisListCache redisListCache;
    @Resource
    private RedisSetCache redisSetCache;
    @Resource
    private RedisStrCache redisStrCache;


    public Cache<String, String> getStringCache(){
        if("memory".equals(cacheType)){
            return memoryStrCache;
        }else if("redis".equals(cacheType)){
            return redisStrCache;
        }else{
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT,"不支持该类型cache");
        }
    }
    public Cache<String, Object> getHashCache(){
        if("memory".equals(cacheType)){
            return memoryHashCache;
        }else if("redis".equals(cacheType)){
            return redisHashCache;
        }else{
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT,"不支持该类型cache");
        }
    }
    public Cache<String, List<String>> getListCache(){
        if("memory".equals(cacheType)){
            return memoryListCache;
        }else if("redis".equals(cacheType)){
            return redisListCache;
        }else{
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT,"不支持该类型cache");
        }
    }

    public Cache<String, Set<String>> getSetCache(){
        if("memory".equals(cacheType)){
            return memorySetCache;
        }else if("redis".equals(cacheType)){
            return redisSetCache;
        }else{
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT,"不支持该类型cache");
        }
    }
}
