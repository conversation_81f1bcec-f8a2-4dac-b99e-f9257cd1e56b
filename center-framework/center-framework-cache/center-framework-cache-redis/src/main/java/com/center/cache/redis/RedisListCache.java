package com.center.cache.redis;


import com.center.cache.interfaces.Cache;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: redis list缓存实现类
 * @date 2024/11/12 17:32
 */
@Component
public class RedisListCache implements Cache<String, List<String>> {


    @Resource
    @Lazy
    private RedisTemplate<String, List<String>> redisTemplate;

    @Override
    public List<String> get(String key) {
        return redisTemplate.opsForList().leftPop(key);
    }

    @Override
    public void put(String key, List<String> value) {
        redisTemplate.opsForList().rightPushAll(key, value);
    }

    @Override
    public void put(String key, List<String> value, Long ttlTime) {
        redisTemplate.opsForList().rightPushAll(key, value);
        redisTemplate.expire(key.toString(), ttlTime, TimeUnit.MILLISECONDS);
    }

    @Override
    public void remove(String key) {
        redisTemplate.delete(key);
    }
}
