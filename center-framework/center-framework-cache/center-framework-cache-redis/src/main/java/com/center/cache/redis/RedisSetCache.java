package com.center.cache.redis;


import com.center.cache.interfaces.Cache;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: redis set缓存实现类
 * @date 2024/11/13 9:28
 */
@Component
public class RedisSetCache implements Cache<String, Set<String>> {


    @Resource
    @Lazy
    private RedisTemplate<String, Set<String>> redisTemplate;

    @Override
    public Set<String> get(String key) {
        Set<Set<String>> value = redisTemplate.opsForSet().members(key);
        if (value == null || value.isEmpty()) {
            return null;
        }
        return value.iterator().next();
    }

    @Override
    public void put(String key, Set<String> value) {
        redisTemplate.opsForSet().add(key, value);
    }

    @Override
    public void put(String key, Set<String> value, Long ttlTime) {
        redisTemplate.opsForSet().add(key, value);
        redisTemplate.expire(key, ttlTime, TimeUnit.MILLISECONDS);
    }

    @Override
    public void remove(String key) {
        redisTemplate.delete(key);
    }
}
