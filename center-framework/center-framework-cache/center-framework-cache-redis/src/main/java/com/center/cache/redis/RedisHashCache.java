package com.center.cache.redis;

import com.center.cache.interfaces.Cache;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: redis hash缓存实现类
 * @date 2024/11/13 9:54
 */
@Component
public class RedisHashCache implements Cache<String, Object> {


    @Resource
    @Lazy
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Object get(String key) {
        return redisTemplate.opsForHash().get(key, key);
    }

    @Override
    public void put(String key, Object value) {
        redisTemplate.opsForHash().put(key, key, value);
    }

    @Override
    public void put(String key, Object value, Long ttlTime) {
        redisTemplate.opsForHash().put(key, key, value);
        redisTemplate.expire(key, ttlTime, TimeUnit.MILLISECONDS);
    }

    @Override
    public void remove(String key) {
        redisTemplate.delete(key);
    }
}
