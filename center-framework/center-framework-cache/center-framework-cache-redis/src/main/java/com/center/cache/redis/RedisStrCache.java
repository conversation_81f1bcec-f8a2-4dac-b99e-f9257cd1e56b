package com.center.cache.redis;


import com.center.cache.interfaces.Cache;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: redis str缓存实现类
 * @date 2024/11/12 16:23
 */
@Component
public class RedisStrCache implements Cache<String, String> {

    @Resource
    @Lazy
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public String get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    @Override
    public void put(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    @Override
    public void put(String key, String value, Long ttlTime) {
        redisTemplate.opsForValue().set(key, value);
        redisTemplate.expire(key, ttlTime, TimeUnit.MILLISECONDS);
    }

    @Override
    public void remove(String key) {
        redisTemplate.delete(key);
    }
}
