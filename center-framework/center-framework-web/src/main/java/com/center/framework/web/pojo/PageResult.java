package com.center.framework.web.pojo;

import com.center.framework.common.utils.object.OrikaUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.springframework.data.domain.Page;

@Schema(description = "分页结果")
@Data
public final class PageResult<T> implements Serializable {

//    @Schema(description = "数据", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<T> list;

//    @Schema(description = "总量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long total;

    private PageResult() {
    }

    private PageResult(List<T> list, Long total) {
        this.list = list;
        this.total = total;
    }

    private PageResult(Long total) {
        this.list = new ArrayList<>();
        this.total = total;
    }

    public static <T> PageResult<T> empty() {
        return new PageResult<>(0L);
    }

    public static <T> PageResult<T> empty(Long total) {
        return new PageResult<>(total);
    }

    public static <T> PageResult<T> of(Page page) {
        if(null == page){
            return empty();
        }
        return new PageResult<>(page.getContent(), page.getTotalElements());
    }

    public static <T> PageResult<T> of(Page page,Class c) {
        if(null == page){
            return empty();
        }
        return new PageResult<>(OrikaUtils.convertList(page.getContent(),c), page.getTotalElements());
    }
    public static <T> PageResult<T> of(List<T> list,Long total) {
        return new PageResult<>(list,total);
    }

}
