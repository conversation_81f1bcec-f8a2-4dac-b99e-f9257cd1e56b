package com.center.framework.web.interceptor;


import static com.center.framework.web.pojo.CommonResult.error;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.web.jwt.JwtTokenProvider;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

@Component
public class LoginValidateInterceptor implements HandlerInterceptor {

  @Resource
  private JwtTokenProvider jwtTokenProvider;


  @Override
  public void afterCompletion(HttpServletRequest request,
      HttpServletResponse response, Object arg2, Exception arg3)
      throws Exception {
//    请求完成后，需要清除ThreadLocal中的数据
    LoginContextHolder.clear();
  }

  @Override
  public void postHandle(HttpServletRequest request,
      HttpServletResponse response, Object arg2, ModelAndView arg3)
      throws Exception {

  }

  @Override
  public boolean preHandle(HttpServletRequest request,
      HttpServletResponse response, Object arg2) throws Exception {

    //1.拦截获取token
    String token = jwtTokenProvider.resolveToken(request);

    if (token != null && jwtTokenProvider.validateToken(token)) {
      LoginContextHolder.setLoginUserId(jwtTokenProvider.getUserId(token));
      LoginContextHolder.setLoginUserTenantId(jwtTokenProvider.getTenantId(token));
      LoginContextHolder.setLoginUserDepartId(jwtTokenProvider.getDepartId(token));
      return true;
    }
    ServletUtil
        .write(response, JSONUtil.toJsonStr(error(GlobalErrorCodeConstants.UNAUTHORIZED)),
            MediaType.APPLICATION_JSON_UTF8_VALUE);
    return false;
  }
}
