package com.center.framework.web.interceptor;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.web.pojo.CommonResult;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;



@Component
public class XssInterceptor implements HandlerInterceptor {

  private static Logger logger = LoggerFactory.getLogger(XssInterceptor.class);

  private static Pattern XSS_PATTERN;

  // 匹配含有字符： <
  public static String regex = ".*<.*";
  // 匹配含有字符： alert( )
  public static String regex1 = ".*[A|a][L|l][E|e][R|r][T|t]\\s*\\(.*\\).*";
  // 匹配含有字符： window.location =
  public static String regex2 = ".*[W|w][I|i][N|n][D|d][O|o][W|w]\\.[L|l][O|o][C|c][A|a][T|t][I|i][O|o][N|n]\\s*=.*";
  // 匹配含有字符：style = x:expression ( )
  public static String regex3 = ".*[S|s][T|t][Y|y][L|l][E|e]\\s*=.*[X|x]:[E|e][X|x].*[P|p][R|r][E|e][S|s]{1,2}[I|i][O|o][N|n]\\s*\\(.*\\).*";
  // 匹配含有字符： document.cookie
  public static String regex4 = ".*[D|d][O|o][C|c][U|u][M|m][E|e][N|n][T|t]\\.[C|c][O|o]{2}[K|k][I|i][E|e].*";
  // 匹配含有字符： eval( )
  public static String regex5 = ".*[E|e][V|v][A|a][L|l]\\s*\\(.*\\).*";
  // 匹配含有字符： unescape()
  public static String regex6 = ".*[U|u][N|n][E|e][S|s][C|c][A|a][P|p][E|e]\\s*\\(.*\\).*";
  // 匹配含有字符： execscript( )
  public static String regex7 = ".*[E|e][X|x][E|e][C|c][S|s][C|c][R|r][I|i][P|p][T|t]\\s*\\(.*\\).*";
  // 匹配含有字符： msgbox( )
  public static String regex8 = ".*[M|m][S|s][G|g][B|b][O|o][X|x]\\s*\\(.*\\).*";
  // 匹配含有字符： confirm( )
  public static String regex9 = ".*[C|c][O|o][N|n][F|f][I|i][R|r][M|m]\\s*\\(.*\\).*";
  // 匹配含有字符： prompt( )
  public static String regex10 = ".*[P|p][R|r][O|o][M|m][P|p][T|t]\\s*\\(.*\\).*";
  // 匹配含有字符： <script> </script>
  public static String regex11 = ".*<[S|s][C|c][R|r][I|i][P|p][T|t]>.*";
  // 匹配含有字符： <script> </script>
  public static String regex12 = ".*<[S|s][C|c][R|r][I|i][P|p][T|t]>.*</[S|s][C|c][R|r][I|i][P|p][T|t]>.*";
  // 匹配含有字符： 含有一个符号： 双引号
  public static String regex13 = "[.&[^\"]]*\"[.&[^\"]]*";
  // 匹配含有字符： 含有一个符号： 单引号
  public static String regex14 = "[.&[^']]*'[.&[^']]*";
  // 匹配含有字符： 含有回车换行 和 <script> </script>
  public static String regex15 = "[[.&[^a]]|[|a|\n|\r\n|\r|\u0085|\u2028|\u2029]]*<[S|s][C|c][R|r][I|i][P|p][T|t]>.*</[S|s][C|c][R|r][I|i][P|p][T|t]>[[.&[^a]]|[|a|\n|\r\n|\r|\u0085|\u2028|\u2029]]*]";
  // 匹配含有字符： <script> </script>
  public static String regex16 = ".*<[J|j][A|a][V|v][A|a][S|s][C|c][R|r][I|i][P|p][T|t]>.*</[J|j][A|a][V|v][A|a][S|s][C|c][R|r][I|i][P|p][T|t]>.*";
  // 匹配含有字符： 含有回车换行 和 <script> </script>
  public static String regex17 = "[[.&[^a]]|[|a|\n|\r\n|\r|\u0085|\u2028|\u2029]]*<[J|j][A|a][V|v][A|a][S|s][C|c][R|r][I|i][P|p][T|t]>.*</[J|j][A|a][V|v][A|a][S|s][C|c][R|r][I|i][P|p][T|t]>[[.&[^a]]|[|a|\n|\r\n|\r|\u0085|\u2028|\u2029]]*]";

  @Override
  public void afterCompletion(HttpServletRequest arg0, HttpServletResponse arg1, Object arg2,
      Exception arg3) throws Exception {

  }

  @Override
  public void postHandle(HttpServletRequest arg0, HttpServletResponse arg1, Object arg2,
      ModelAndView arg3) throws Exception {

  }

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
      throws Exception {
    StringBuffer str = new StringBuffer();
    Map<String, String[]> map = request.getParameterMap();
    if (map != null && map.size() != 0) {
      Set<String> names = map.keySet();
      if (CollectionUtil.isNotEmpty(names)) {
        Iterator<String> iterator = names.iterator();
        while (iterator.hasNext()) {
          String value = request.getParameter(iterator.next());
          str.append(value);
        }
      }
    }
    if (Pattern.compile(regex).matcher(str.toString()).matches()
        || Pattern.compile(regex1).matcher(str.toString()).matches()
        || Pattern.compile(regex2).matcher(str.toString()).matches()
        || Pattern.compile(regex3).matcher(str.toString()).matches()
        || Pattern.compile(regex4).matcher(str.toString()).matches()
        || Pattern.compile(regex5).matcher(str.toString()).matches()
        || Pattern.compile(regex6).matcher(str.toString()).matches()
        || Pattern.compile(regex7).matcher(str.toString()).matches()
        || Pattern.compile(regex8).matcher(str.toString()).matches()
        || Pattern.compile(regex9).matcher(str.toString()).matches()
        || Pattern.compile(regex10).matcher(str.toString()).matches()
        || Pattern.compile(regex11).matcher(str.toString()).matches()
        || Pattern.compile(regex12).matcher(str.toString()).matches()
        || Pattern.compile(regex13).matcher(str.toString()).matches()
        || Pattern.compile(regex14).matcher(str.toString()).matches()
        || Pattern.compile(regex15).matcher(str.toString()).matches()
        || Pattern.compile(regex16).matcher(str.toString()).matches()
        || Pattern.compile(regex17).matcher(str.toString()).matches()) {
      response.setCharacterEncoding("UTF-8");
      response.setContentType("application/json");

      response.getWriter().print(JSON.toJSONString(
          CommonResult.error(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR.getCode(),
              GlobalErrorCodeConstants.REQUEST_PARAM_ERROR.getMessage())
          , true));
      return false;
    } else {
      return true;
    }
  }
}
