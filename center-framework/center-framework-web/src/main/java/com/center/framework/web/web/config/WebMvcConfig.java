package com.center.framework.web.web.config;

import com.center.framework.web.interceptor.ServerInterceptor;
import com.center.framework.web.interceptor.XssInterceptor;
import com.center.framework.web.interceptor.LoginValidateInterceptor;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

  @Resource
  private XssInterceptor xssInterceptor;

  @Resource
  private LoginValidateInterceptor loginValidateInterceptor;

  @Resource
  private ServerInterceptor serverInterceptor;


  @Value("${center.ignore-urls}")
  private String[] ignoreUrls;
  @Value("${center.server-urls}")
  private String[] serverUrls;


  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(xssInterceptor)
        .addPathPatterns("/**");

    registry.addInterceptor(loginValidateInterceptor).addPathPatterns("/**")
        .excludePathPatterns(getExcludePathPatterns());
    registry.addInterceptor(serverInterceptor).addPathPatterns(getServerPathPatterns());
  }

  @Override
  public void addResourceHandlers(ResourceHandlerRegistry registry) {
    registry.addResourceHandler("swagger-ui.html")
        .addResourceLocations("classpath:/META-INF/resources/");
    registry.addResourceHandler("/webjars/**")
        .addResourceLocations("classpath:/META-INF/resources/webjars/");
  }

  /**
   * 创建 CorsFilter Bean，解决跨域问题
   */
  @Bean
  public FilterRegistrationBean<CorsFilter> corsFilterBean() {
    // 创建 CorsConfiguration 对象
    CorsConfiguration config = new CorsConfiguration();
    config.setAllowCredentials(true);
    config.addAllowedOriginPattern("*"); // 设置访问源地址
    config.addAllowedHeader("*"); // 设置访问源请求头
    config.addAllowedMethod("*"); // 设置访问源请求方法
    // 创建 UrlBasedCorsConfigurationSource 对象
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", config); // 对接口配置跨域设置
    FilterRegistrationBean<CorsFilter> bean = new FilterRegistrationBean<>(new CorsFilter(source));
    bean.setOrder(Integer.MIN_VALUE);
    return bean;
  }

  /**
   * spring boot 解决前端处理Long溢出问题（转字符串）
   * @return
   */
  @Bean("jackson2ObjectMapperBuilderCustomizer")
  public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
    Jackson2ObjectMapperBuilderCustomizer customizer = new Jackson2ObjectMapperBuilderCustomizer() {
      @Override
      public void customize(Jackson2ObjectMapperBuilder jacksonObjectMapperBuilder) {
        jacksonObjectMapperBuilder.serializerByType(Long.class, ToStringSerializer.instance)
            .serializerByType(Long.TYPE, ToStringSerializer.instance);
      }
    };
    return customizer;
  }

  private List getExcludePathPatterns(){
    List<String> list = new ArrayList<>();
      if(null != ignoreUrls){
        Collections.addAll(list,ignoreUrls);
      }
      list.addAll(getServerPathPatterns());
    return list;
  }

  private List getServerPathPatterns(){
    List<String> list = new ArrayList<>();
    if(null != serverUrls){
      Collections.addAll(list,serverUrls);
    }
    return list;
  }
}
