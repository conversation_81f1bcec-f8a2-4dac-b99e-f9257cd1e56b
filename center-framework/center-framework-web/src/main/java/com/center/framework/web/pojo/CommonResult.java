package com.center.framework.web.pojo;

import cn.hutool.core.util.StrUtil;
import com.center.framework.common.exception.ErrorCode;
import com.center.framework.common.exception.ServiceException;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.Objects;
import lombok.Data;
import org.springframework.util.Assert;

/**
 * 通用返回
 *
 * @param <T> 数据泛型
 */
@Data
public class CommonResult<T> implements Serializable {

    private static String SUCCESS="success";

    /**
     * 错误码
     *
     * @see ErrorCode#getCode()
     */
    private Integer code;
    /**
     * 返回数据
     */
    private T data;
    /**
     * 错误提示，用户可阅读
     *
     * @see ErrorCode#getMessage() ()
     */
    private String message;

    private String stackTrace;

    /**
     * 将传入的 result 对象，转换成另外一个泛型结果的对象
     *
     * 因为 A 方法返回的 CommonResult 对象，不满足调用其的 B 方法的返回，所以需要进行转换。
     *
     * @param result 传入的 result 对象
     * @param <T>    返回的泛型
     * @return 新的 CommonResult 对象
     */
    public static <T> CommonResult<T> error(CommonResult<?> result) {
        return error(result.getCode(), result.getMessage());
    }

    public static <T> CommonResult<T> error(Integer code, String message) {
        Assert.isTrue(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code), "code 必须是错误的！");
        CommonResult<T> result = new CommonResult<>();
        result.code = code;
        result.message = message;
        result.data= (T) StrUtil.EMPTY;
        result.stackTrace = StrUtil.EMPTY;
        return result;
    }
    public static <T> CommonResult<T> error(Integer code, String message,String stackTrace) {
        Assert.isTrue(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code), "code 必须是错误的！");
        CommonResult<T> result = new CommonResult<>();
        result.code = code;
        result.message = message;
        result.data= (T) StrUtil.EMPTY;
        result.stackTrace = stackTrace;
        return result;
    }

    public static <T> CommonResult<T> error(ErrorCode errorCode) {
        return error(errorCode.getCode(), errorCode.getMessage());
    }

    public static <T> CommonResult<T> success(T data) {
        CommonResult<T> result = new CommonResult<>();
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.data = data;
        result.message = SUCCESS;
        result.stackTrace = StrUtil.EMPTY;
        return result;
    }
    public static <T> CommonResult<T> success() {
        CommonResult<T> result = new CommonResult<>();
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.data = (T)StrUtil.EMPTY;
        result.message = SUCCESS;
        result.stackTrace = StrUtil.EMPTY;
        return result;
    }

    public static boolean isSuccess(Integer code) {
        return Objects.equals(code, GlobalErrorCodeConstants.SUCCESS.getCode());
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isSuccess() {
        return isSuccess(code);
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isError() {
        return !isSuccess();
    }

    // ========= 和 Exception 异常体系集成 =========

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
     */
    public void checkError() throws ServiceException {
        if (isSuccess()) {
            return;
        }
        // 业务异常
        throw new ServiceException(code, message);
    }

//    /**
//     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
//     * 如果没有，则返回 {@link #data} 数据
//     */
//    @JsonIgnore // 避免 jackson 序列化
//    public T getCheckedData() {
////        checkError();
//        return data;
//    }

    public static <T> CommonResult<T> error(ServiceException serviceException) {
        return error(serviceException.getCode(), serviceException.getMessage());
    }
    /**
     * 创建一个成功响应结果，仅包含自定义消息
     * 该方法用于当API调用成功，但不需要返回具体数据时使用
     * 它将返回一个带有成功状态码和自定义消息的CommonResult对象
     *
     * @param message 自定义的成功消息
     * @param <T> 泛型参数，表示结果数据的类型
     * @return CommonResult<T> 包含成功状态码和自定义消息的结果对象
     */
    public static <T> CommonResult<T> successWithMessageOnly(String message) {
        CommonResult<T> result = new CommonResult<>();
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        // 保持 data 为空
        result.data = (T) StrUtil.EMPTY;
        // 设置自定义 message
        result.message = message;
        result.stackTrace = StrUtil.EMPTY;
        return result;
    }
    /**
     * 创建一个表示成功操作的通用结果对象
     * 此方法用于封装成功的结果数据和消息，提供给上层调用者
     * 它允许在返回成功消息时携带额外的数据和自定义消息
     *
     * @param data    成功操作后返回的数据，可以是任意类型
     * @param message 自定义的成功消息，用于提供更具体的成功信息
     * @return 返回一个封装了成功代码、数据和消息的CommonResult对象
     */
    public static <T> CommonResult<T> success(T data, String message) {
        CommonResult<T> result = new CommonResult<>();
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.data = data;
        result.message = message;  // 设置自定义消息
        result.stackTrace = StrUtil.EMPTY;
        return result;
    }


}
