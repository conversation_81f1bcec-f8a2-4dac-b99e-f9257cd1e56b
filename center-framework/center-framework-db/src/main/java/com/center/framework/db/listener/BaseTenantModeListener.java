package com.center.framework.db.listener;

import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.db.annotation.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.stereotype.Component;

import javax.persistence.PrePersist;
import java.lang.reflect.Field;

/**
 * JPA对对象监听器，如果字段上指定了@TenantId这个注解，
 * 则在对象保存进数据库前，系统自动给ID字段赋值
 */

@Component
@Slf4j
public class BaseTenantModeListener {

  @PrePersist
  public void PrePersist(final Object object){
    Class clazz = object.getClass();
    while (true){
      if(setIdValue(clazz.getDeclaredFields(),object)){
        break;
      }
      if(clazz.getSuperclass() != null){
        clazz = clazz.getSuperclass();
      }else {
        log.error("此对象添加了@BaseTenantModelListener,但没有配置@TenantId注解。"+object.getClass());
        break;
      }
    }

  }

  private boolean setIdValue(Field[] fields,Object object){
    for(Field field:fields){
      if(field.isAnnotationPresent(TenantId.class)){
        BeanWrapper wrapper = new BeanWrapperImpl(object);
        if(wrapper.getPropertyValue(field.getName()) == null){
          wrapper.setPropertyValue(field.getName(), LoginContextHolder.getLoginUserTenantId());
        }
        return true;
      }
    }
    return false;
  }

}
