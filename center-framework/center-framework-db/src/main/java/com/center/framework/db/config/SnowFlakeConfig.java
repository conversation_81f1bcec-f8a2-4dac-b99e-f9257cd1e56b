package com.center.framework.db.config;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class SnowFlakeConfig {


  @Value("${center.snowflake.data-center-id}")
  private Long dataCenterId;

  @Value("${center.snowflake.max-worker-id}")
  private Long  maxWorkerId;

  @Bean
  public Snowflake snowFlakeCore(){
    Long workId = IdUtil.getWorkerId(dataCenterId,maxWorkerId);
    return IdUtil.getSnowflake(workId);
  }
}
