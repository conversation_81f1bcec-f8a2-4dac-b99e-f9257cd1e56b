package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum KnowledgeTypeEnum implements IEnumerate<String> {

    INDIVIDUAL("INDIVIDUAL","个人知识库"),
    ENTERPRISE("ENTERPRISE", "企业知识库");

    private final String value;
    private final String description;

    @Override
    public String getValue() {
        return null;
    }

    @Override
    public String getDescription() {
        return null;
    }
}
