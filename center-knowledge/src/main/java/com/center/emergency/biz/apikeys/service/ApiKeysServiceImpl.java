package com.center.emergency.biz.apikeys.service;

import com.center.emergency.biz.apikeys.persitence.ApiKeys;
import com.center.emergency.biz.apikeys.persitence.ApiKeysRepository;
import com.center.emergency.biz.apikeys.persitence.QApiKeys;
import com.center.emergency.biz.apikeys.pojo.*;
import com.center.emergency.biz.apikeys.service.ApiKeysChatUtils;
import com.center.emergency.biz.model.persistence.QLargeModel;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.emergency.biz.robot.persitence.QRobotModel;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageParam;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import com.center.infrastructure.system.biz.user.persistence.UserModel;
import com.center.infrastructure.system.biz.user.persistence.UserRepository;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.*;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.*;

@Service
@Slf4j
public class ApiKeysServiceImpl implements ApiKeysService{

    @Autowired
    private ApiKeysRepository apiKeysRepository;

    @Autowired
    private JPAQueryFactory queryFactory;

    @Resource
    private DepartRepository departRepository;

    @Resource
    private UserRepository userRepository;

    @Autowired
    private ApiKeysChatUtils apiKeysChatUtils;

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    @Override
    @Transactional
    public Long createApiKey(ApiKeyCreateReq req) {
        //1. 机器人内名称判重
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        if (apiKeysRepository.existsByTenantIdAndRobotIdAndApiKeyName(tenantId, req.getRobotId(), req.getApiKeyName())){
            throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "该机器人下ApiKey名称重复，请更换");
        }
        //2. ApiKey 全局唯一
        if (apiKeysRepository.existsByApiKey(req.getApiKey())){
            throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "ApiKey重复，请更换");
        }

        ApiKeys apiKey = OrikaUtils.convert(req, ApiKeys.class);
        return apiKeysRepository.save(apiKey).getId();
    }

    @Override
    public String generatorKey() {
        SecureRandom random = new SecureRandom();
        StringBuilder key = new StringBuilder("sk-");
        for (int i = 0; i < 32; i++) {
            key.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return key.toString();
    }

    @Override
    @Transactional
    public void deleteApiKey(Long id) {
        // 1. 校验 API Key 是否存在
        ApiKeys apiKey = apiKeysRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "API Key 不存在"));

        // 2. 判断删除操作权限（假设有 tenantId 字段）
        if (!apiKey.getTenantId().equals(LoginContextHolder.getLoginUserTenantId())) {
            throw ServiceExceptionUtil.exception(FORBIDDEN, "没有删除该 API Key 的权限");
        }

        // 3. 执行删除
        apiKeysRepository.deleteById(id);
    }

    @Override
    public void updateApiKey(ApiKeyUpdateReq req) {
        Long keyId = req.getId();

        // 1. 校验 API Key 是否存在
        ApiKeys apiKey = apiKeysRepository.findById(keyId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "API Key 不存在"));

        // 2. 判断操作权限（租户隔离）
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        if (!tenantId.equals(apiKey.getTenantId())) {
            throw ServiceExceptionUtil.exception(FORBIDDEN, "没有更新该 API Key 的权限");
        }

        // 3. 名称在该机器人内判重
        if (!apiKey.getApiKeyName().equals(req.getApiKeyName())
                && apiKeysRepository.existsByTenantIdAndRobotIdAndApiKeyName(tenantId, apiKey.getRobotId(), req.getApiKeyName())) {
            throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "该机器人下密钥名称重复，请更换");
        }

        // 5. 更新可变字段
        apiKey.setApiKeyName(req.getApiKeyName());
        apiKey.setModelId(req.getModelId());
        if (req.getStartTime() != null) {
            apiKey.setStartTime(req.getStartTime());
        }
        if (req.getEndTime() != null) {
            apiKey.setEndTime(req.getEndTime());
        }
        // 6. 保存更新
        apiKeysRepository.save(apiKey);
    }

    @Override
    public ApiKeyResp getApiKeyDetail(Long id) {
        QApiKeys qApiKeys = QApiKeys.apiKeys;
        QLargeModel qLargeModel = QLargeModel.largeModel;

        ApiKeyResp apiKeyResp = queryFactory.select((
                        Projections.bean(
                                ApiKeyResp.class,
                                qApiKeys.id,
                                qApiKeys.apiKey,
                                qApiKeys.apiKeyName,
                                qApiKeys.modelId,
                                qApiKeys.startTime,
                                qApiKeys.endTime,
                                qApiKeys.tenantId,
                                qLargeModel.modelDisplayName
                        )))
                .from(qApiKeys)
                .leftJoin(qLargeModel).on(qApiKeys.modelId.eq(qLargeModel.id))
                .where(qApiKeys.id.eq(id))
                .fetchOne();

        if (apiKeyResp == null) {
            throw ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "API Key 不存在");
        }
        //2. 判断操作权限
        if (!apiKeyResp.getTenantId().equals(LoginContextHolder.getLoginUserTenantId())) {
            throw ServiceExceptionUtil.exception(FORBIDDEN, "没有获取API Key详情权限");
        }

        //3. 隐藏Api key
        apiKeyResp.setApiKey(maskApiKey(apiKeyResp.getApiKey()));
        return apiKeyResp;
    }

    /**
     * 对完整的 API Key 字符串进行脱敏：保留前 4 位和后 4 位，中间以 '*' 替代。
     *
     * @param apiKey 原始完整 API Key
     * @return 脱敏后的 API Key，如果输入无效则返回 "******"
     */
    public String maskApiKey(String apiKey) {
        if (apiKey != null && apiKey.length() >= 8) {
            int maskLength = apiKey.length() - 8;
            StringBuilder sb = new StringBuilder();
            sb.append(apiKey.substring(0, 4));
            for (int i = 0; i < maskLength; i++) {
                sb.append("*");
            }
            sb.append(apiKey.substring(apiKey.length() - 4));
            return sb.toString();
        }
        return "******";
    }


    @Override
    public PageResult<ApiKeyPageResp> pageApiKey(ApiKeyPageReq req) {
        // 1. 构建分页条件
        Pageable pageable = PageRequest.of(req.getPageNo() - 1, req.getPageSize());

        QApiKeys qApiKey = QApiKeys.apiKeys;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qApiKey.tenantId.eq(LoginContextHolder.getLoginUserTenantId()));
        builder.and(qApiKey.robotId.eq(req.getRobotId()));

        JPQLQuery<ApiKeys> jpqlQuery = queryFactory.selectFrom(qApiKey)
                .where(builder)
                .orderBy(qApiKey.updateTime.desc())
                .offset(pageable.getOffset()) // 使用 pageable 的偏移量
                .limit(pageable.getPageSize()); // 设置每页记录数;

        Long total = jpqlQuery.fetchCount();
        List<ApiKeys> apiKeysList = jpqlQuery.fetch();

        List<ApiKeyPageResp> resultList = new ArrayList<>();
        for (ApiKeys apiKeys : apiKeysList) {
            apiKeys.setApiKey(maskApiKey(apiKeys.getApiKey()));
            ApiKeyPageResp apiKeyPageResp = OrikaUtils.convert(apiKeys, ApiKeyPageResp.class);
            resultList.add(apiKeyPageResp);
        }

        return PageResult.of(resultList, total);
    }

    @Override
    public ValidateResp validateApiKey(ApiKeyValidateReq req) {
        // 1. 查询 API Key 是否存在
        Optional<ApiKeys> key = apiKeysRepository.findByApiKey(req.getApi_key());

        ValidateResp validateResp = new ValidateResp();
        validateResp.setRequestId(req.getRequest_id());
        validateResp.setTimestamp(System.currentTimeMillis());
        if (key.isPresent()) {

            // 2. 判断是否已过期--可为空，表示永久有效
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endTime = key.get().getEndTime();
            if (endTime != null && now.isAfter(endTime)){
                throw ServiceExceptionUtil.exception(FORBIDDEN, "API Key已过期");
            }else {

                //3. 校验成功，设置返回
                ValidateResp.Config config = new ValidateResp.Config();
                Optional<ApiKeys> apiKey = apiKeysRepository.findByApiKey(req.getApi_key());

                QLargeModel qLargeModel = QLargeModel.largeModel;
                QRobotModel qRobotModel = QRobotModel.robotModel;
                QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
                BooleanBuilder builder = new BooleanBuilder();
                builder.and(qRobotKnowledgeModel.robotId.eq(apiKey.get().getRobotId()));
                builder.and(qLargeModel.id.eq(apiKey.get().getModelId()));
                builder.and(qRobotModel.id.eq(apiKey.get().getRobotId()));

                ApiKeyValidateDTO apiKeyValidateDTO = queryFactory.select(Projections.bean(
                                ApiKeyValidateDTO.class,
                                qRobotKnowledgeModel.id.as("kbId"),
                                qLargeModel.maxTokens.as("maxToken"),
                                qRobotModel.id.as("robotId"),
                                qRobotModel.searchMode,
                                qRobotModel.similarityThreshold.as("score_threshold"),
                                qLargeModel.inputContextLength.as("apiContextLength"),
                                qLargeModel.temperature,
                                qLargeModel.topP,
                                qRobotModel.maxHits.as("topK"),
                                qRobotModel.answerMode,
                                qLargeModel.modelName.as("model"),
                                qLargeModel.baseUrl.as("apiBase"),
                                qLargeModel.apiKey.as("api_key")
                        ))
                        .from(qRobotModel, qLargeModel, qRobotKnowledgeModel)
                        .where(builder)
                        .fetchOne();
                if (apiKeyValidateDTO == null) {
                    throw ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "API Key不存在");
                }else {
                    Optional<UserModel> userModel = userRepository.findById(key.get().getCreatorId());
                    Long departId = userModel.get().getDepartId();
                    Long tenantId = userModel.get().getTenantId();

                    List<Long> tagsList = apiKeysChatUtils.getKnowledgeBaseTagIds(apiKeyValidateDTO.getKbId(), queryFactory);
                    Set<String> kbidSet = new HashSet<>();
                    List<String> filedList = new ArrayList<>();
                    apiKeysChatUtils.buildRobotResourcesForApiKey(apiKeyValidateDTO.getRobotId(), kbidSet, filedList, queryFactory, departId, tenantId, departRepository);

                    config.setKb_ids(kbidSet);
                    config.setFile_ids(filedList);
                    config.setStreaming(1);
                    config.setHistory(new ArrayList<>());
                    config.setTags_list(tagsList);
                    config.setMax_token(apiKeyValidateDTO.getMaxToken());
                    config.setSearch_mode(apiKeyValidateDTO.getSearchMode().getDescription());
                    config.setScore_threshold(apiKeyValidateDTO.getScore_threshold());
                    config.setRerank(1);
                    config.setOnly_need_search_results(0);
                    config.setApi_context_length(apiKeyValidateDTO.getApiContextLength());
                    config.setTemperature(apiKeyValidateDTO.getTemperature());
                    config.setTop_p(apiKeyValidateDTO.getTopP());
                    config.setTop_k(apiKeyValidateDTO.getTopK());
                    config.setAnswer_mode(apiKeyValidateDTO.getAnswerMode().getDescription());
                    config.setQuery_decompose(1);
                    config.setModel(apiKeyValidateDTO.getModel());
                    config.setApi_base(apiKeyValidateDTO.getApiBase());
                    config.setApi_key(apiKeyValidateDTO.getApi_key());

                    validateResp.setValid(true);
                    validateResp.setConfig(config);
                }

            }
        }else{
            throw ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "API Key不存在");
        }
        return validateResp;
    }

}
