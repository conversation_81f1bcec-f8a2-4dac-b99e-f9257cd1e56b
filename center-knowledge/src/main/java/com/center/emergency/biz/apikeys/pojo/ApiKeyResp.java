package com.center.emergency.biz.apikeys.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class ApiKeyResp {
    @Schema(description = "ApiKey的ID")
    private Long id;

    @Schema(description = "ApiKey名称",example = "ApiKey名称")
    private String apiKeyName;

    @Schema(description = "模型的ID", example = "1914200736866541568")
    private Long modelId;

    @Schema(description = "模型的名称", example = "模型名称")
    private String modelDisplayName;

    @Schema(description = "密钥")
    private String apiKey;

    @Schema(description = "开始日期",example = "2025-05-28 10:23:09")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "截止日期",example = "2025-05-28 10:23:09")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @JsonIgnore
    @Schema(description = "tenantId")
    private Long tenantId;
}
