package com.center.emergency.biz.knowledgebase.persistence;


import com.center.emergency.common.enumeration.KnowledgeTypeEnum;
import com.center.framework.db.core.BaseDeleteModel;
import com.center.framework.db.core.BaseModel;
import com.center.framework.db.core.BaseTenantModel;
import com.querydsl.core.annotations.QueryEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * 实体类：表示知识库表的实体映射
 */
@Data
@Entity
@QueryEntity
@Table(name = "center_knowledge_bases")
//@Where(clause = "is_deleted = 0")  // 默认只查询未删除的数据
public class KnowledgeBaseModel extends BaseTenantModel {

    @Schema(description = "知识库名称")
    @Column(name = "kb_name", nullable = false)
    private String kbName;

    @Schema(description = "所属部门ID")
    @Column(name = "department_id", nullable = false)
    private Long departmentId;

    @Schema(description = "模型文件库ID")
    @Column(name = "ai_fileb_id", length = 255)
    private String aiFilebId;

    @Schema(description = "模型知识库ID")
    @Column(name = "ai_faqb_id", length = 255)
    private String aiFaqbId;

    @Schema(description = "知识库类型")
    @Column(name = "kb_type", length = 20)
    @Enumerated(EnumType.STRING)
    private KnowledgeTypeEnum kbType;
}
