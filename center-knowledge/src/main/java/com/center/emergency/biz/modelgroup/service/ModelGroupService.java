package com.center.emergency.biz.modelgroup.service;

import com.center.emergency.biz.modelgroup.pojo.*;
import com.center.framework.web.pojo.PageResult;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

public interface ModelGroupService {
    /**
     * 创建模型组
     *
     * @param req 创建请求参数
     * @return 创建成功后的模型组id
     */
    Long createModelGroup(ModelGroupCreateReq req);

    Long deleteModelGroup(Long id);

    ModelGroupResp updateModelGroup(ModelGroupUpdateReq req);

    PageResult<ModelGroupPageResp> pageModelGroup(ModelGroupPageReq req);

    ModelGroupDetailResp getModelGroup(Long id);

    List<LargeModelBase> listLargeModel();

    /**
     * 检查模型组是否存在
     *
     * @param modelGroupId 模型组ID
     * @return 是否存在
     */
    boolean isModelGroupExists(Long modelGroupId);

    /**
     * 构建模型组配置
     * 返回算法接口需要的model_group格式
     *
     * @param modelGroupId 模型组ID，如果为null则使用系统默认
     * @return 模型组配置列表
     */
    List<Map<String, Object>> buildModelGroupConfig(Long modelGroupId);

    /**
     * 获取系统内置模型组ID
     *
     * @return 系统内置模型组ID，如果不存在则返回null
     */
    Long getSystemDefaultModelGroupId();

    /**
     * 获取系统默认模型配置
     * 当模型组不存在时的回退方案
     *
     * @return 系统默认模型配置
     */
    Map<String, Object> getSystemDefaultModelConfig();

    /**
     * 查询模型组下拉列表（包含模型信息）
     *
     * @return 模型组列表
     */
    List<ModelGroupListResp> listModelGroup();

    /**
     * 检查模型是否属于指定模型组
     *
     * @param modelGroupId 模型组ID
     * @param modelId 模型ID
     * @return 是否属于该模型组
     */
    boolean isModelInGroup(Long modelGroupId, Long modelId);

    /**
     * 构建单个模型配置
     *
     * @param modelGroupId 模型组ID
     * @param modelId 模型ID
     * @return 单个模型配置列表
     */
    List<Map<String, Object>> buildSingleModelConfig(Long modelGroupId, Long modelId);

    /**
     * 获取模型组中的所有模型列表（用于前端下拉选择）
     *
     * @param modelGroupId 模型组ID
     * @return 模型列表
     */
    List<LargeModelBase> getModelsByGroupId(Long modelGroupId);
}
