package com.center.emergency.biz.apikeys.persitence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Optional;

@Repository
public interface ApiKeysRepository extends JpaRepository<ApiKeys, Long> {
    boolean existsByTenantIdAndApiKeyName(Long tenantId, String apiKeyName);

    boolean existsByTenantIdAndRobotIdAndApiKeyName(Long tenantId, Long robotId, String apiKeyName);

    Optional<ApiKeys> findByApiKey(String apiKey);

    boolean existsByApiKey( String apiKey);

    Optional<ApiKeys> findByTenantIdAndApiKey(Long loginUserTenantId, String apiKey);
}
