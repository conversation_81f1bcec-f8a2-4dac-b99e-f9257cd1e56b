package com.center.emergency.biz.chat.pojo;

import com.center.emergency.common.enumeration.ChatTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "机器人对话")
public class ChatWithRobotReq {

    @NotBlank(message = "问题不能为空")
    @Schema(description = "问题内容")
    private String question;

    @Schema(description = "机器人ID")
    @NotNull(message = "机器人ID不能为空")
    private Long robotId;

    @Schema(description = "对话SessionID")
    private Long sessionId;

    @Schema(description = "模型ID")
    private Long modelId;

    @Schema(description = "对话类型", example = "NORMAL")
    private ChatTypeEnum chatType;

    @Schema(description = "是否启用模型路由，1为是，0为否")
    private Integer enableModelRouter;

    @Schema(description = "是否启用推理模式，1为是，0为否")
    private Integer enableReasoning;

    @Schema(description = "文件ID列表，用于文件问答模式")
    private List<String> fileIds;

    @Schema(description = "MCP服务ID列表，用于指定特定的MCP服务。为空时使用机器人关联的所有MCP")
    private List<Long> mcpIds;
}
