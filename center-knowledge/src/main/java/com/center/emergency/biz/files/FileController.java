package com.center.emergency.biz.files;

import cn.hutool.json.JSON;
import com.alibaba.fastjson.JSONObject;
import com.center.emergency.biz.files.pojo.*;
import com.center.emergency.biz.files.pojo.filechunk.Chunk;
import com.center.emergency.biz.files.service.FileComponent;
import com.center.emergency.biz.files.service.FileService;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseModel;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseRepository;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.knowledge.KnowledgeApiTool;
import com.center.framework.storage.factory.FileStorageServiceFactory;
import com.center.framework.storage.interfaces.FileStorageService;
import com.center.framework.storage.interfaces.pojo.FileListResp;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.io.IOException;


/**
 * 文件控制器类
 * <AUTHOR>
 */
@RestController
@Tag(name = "文件管理", description = "提供文件上传，新增，修改删除等操作")
@RequestMapping("/file")
@Validated
@Slf4j
public class FileController {

    @Resource
    private FileService fileService;


    @Autowired
    private FileStorageServiceFactory fileStorageServiceFactory;

    @Value("${python.api-base-url}")
    private String pythonApiBaseUrl;

    @Resource
    KnowledgeBaseRepository knowledgeBaseRepository;

    @Resource
    private FileComponent fileComponent;

    @Operation(summary = "本地文件上传,单选",description = "本地文件上传，并异步调用Python接口")
    @PostMapping(value = "/uploadFromLocal", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult<String> uploadFromLocal(
            @Parameter(description = "要上传的文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "知识库ID", required = true)
            @RequestParam("kbId") Long kbId,
            @Parameter(description = "文件名", required = true)
            @RequestParam("fileName") String fileName,
            @Parameter(description = "重命名", required = true)
            @RequestParam("rename") Integer rename ) {
        // 调用事务性集成方法
        FileUploadReq uploadReq = fileService.uploadFileFromLocalWithTransaction(file, kbId, fileName, file.getOriginalFilename(), rename);
        // 返回响应
        return CommonResult.successWithMessageOnly("文件上传成功，后端正在处理加载文件！");
    }

    @Operation(summary = "对话和模拟对话上传本地文件（单选）",description = "目前是同步上传，后续遇到性能问题，可以改为异步")
    @PostMapping(value = "/upload_from_local_for_dailogue", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult<Long> uploadFromLocalForSimulateDailogue(
            @Parameter(description = "要上传的文件", required = true)
            @RequestParam("file") MultipartFile file){
        return CommonResult.success(fileService.uploadFileFromLocalForDailogue(file));
    }


    @Operation(summary = "云盘文件迁移，可以多选文件夹，或文件，具体根据实际业务测试", description = "云端文件上传")
    @PostMapping("/uploadFromCloud")
    public CommonResult<String> uploadFileFromCloud(
            @RequestBody @Valid CloudFileUploadReq cloudFileUploadReq) {
        // 获取请求参数
        Long kbId = cloudFileUploadReq.getKbId();
        List<String> filePaths = cloudFileUploadReq.getFilePaths();
        List<String> dirPaths = cloudFileUploadReq.getDirPaths();

        // 调用服务层方法，集成上传和数据库存储
        List<FileUploadReq> fileUploadReqs = fileService.uploadAndSaveFilesFromCloud(filePaths, kbId, dirPaths);

        // 异步调用 Python 接口批量处理
        fileService.asyncCallPythonUploadBatch(fileUploadReqs);

        // 返回成功信息
        return CommonResult.successWithMessageOnly("文件上传成功，后端正在处理加载文件！请耐心等待");
    }



    /**
     * 更新文件
     * @param req 文件更新请求体
     * @return 成功响应
     */
    @Operation(summary = "更新文件以及文件的标签信息",description = "更新文件，同步可以传入标签")
    @PostMapping("/update")
    public CommonResult<String> updateFile(@Valid @RequestBody FileUpdateReq req) {
        fileService.updateFile(req);
        return CommonResult.successWithMessageOnly("文件更新成功");
    }

    /**
     * 删除文件
     * @param id 文件ID
     * @return 成功响应
     */
    @Operation(summary = "删除文件，只需要传入id",description = "根据id删除文件")
    @PostMapping("/delete/{id}")
    public CommonResult<String> deleteFile(@PathVariable Long id) {
        fileService.deleteFile(id);
        return CommonResult.successWithMessageOnly("文件删除成功");
    }

    /**
     * 获取文件详情
     * @param id
     * @return 文件详情
     */
    @Operation(summary = "主要用于查询文件详情，根据条件查询文件或者单个文件的集合。",description = "根据条件查询单个文件或者文件集合")
    @GetMapping("/getFilesByQuery/{id}")
    @EnumConvertPoint
    public CommonResult<FileResp> getFileByQuery(@PathVariable Long id) {
        // 根据传入的查询条件获取文件信息
        return CommonResult.success(fileService.getFileByQuery(id));
    }


    /**
     * 根据知识库ID获取文件列表
     * @param kbId 知识库ID
     * @return 文件列表
     */
    @GetMapping("/list/{kbId}")
    @EnumConvertPoint
    @Operation(summary = "根据知识库id获取文件详情列表",description = "传入知识库id返回相关文件集合")
    public CommonResult<List<FileResp>> getFilesByKnowledgeBaseId(@PathVariable Long kbId) {
        List<FileResp> files = fileService.getFilesByKnowledgeBaseId(kbId);
        return CommonResult.success(files);
    }

    @GetMapping("/listPageFileByKbid")
    @Operation(summary = "分页-根据知识库ID获取文件详情列表", description = "传入知识库ID和分页参数返回相关文件集合")
    @EnumConvertPoint
    public CommonResult<PageResult<FileResp>> getFilesByKnowledgeBaseId(@Valid FilePageQueryReq filePageQueryReq) {
        // 调用分页获取文件的方法
        PageResult<FileResp> files = fileService.getFilesByKnowledgeBaseId(filePageQueryReq);
        return CommonResult.success(files);
    }


    @PostMapping("/updateStatusAndTagsByPython")
    @Operation(summary = "更新文件状态和标签（用于算法调用）", description = "根据请求体更新文件状态及其标签信息")
    public CommonResult<String> updateFileStatusAndTags(
            @Valid @RequestBody FileUploadPythonReq fileUploadPythonReq) {

        // 调用服务层方法更新文件状态和标签信息
        fileService.updateFileStatusAndTagsByPython(fileUploadPythonReq);

        return CommonResult.successWithMessageOnly("文件状态及标签更新成功");
    }

    @GetMapping("/list_hdfs")
    @Operation(summary = "查询目录下文件(夹)")
    @Parameter(description = "文件夹路径")
    public CommonResult<List<FileListResp>> viewAllFilesFromHdfs(@RequestParam String path) {
        return CommonResult.success(fileService.getAllFiles(path));
    }

    @GetMapping("/getDocChunks")
    @Operation(summary = "获取文档解析内容,传入知识库id，文件id")
    public CommonResult<JSONObject> getDocChunks(@RequestParam Long kbId, @RequestParam Long fileId) throws Exception {
        // 获取知识库的 kb_id
        String kbfileId = knowledgeBaseRepository.findById(kbId)
                .map(KnowledgeBaseModel::getAiFilebId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"));

        // 构造请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");  // 固定 user_id
        requestBody.put("kb_id", kbfileId);
        requestBody.put("file_id", fileId.toString());
        requestBody.put("page_id", 1);  // 写死 page_id
        requestBody.put("page_limit", 10000);  // 写死 page_limit

        // 调用远程 Python API

        String apiUrl = pythonApiBaseUrl + "/api/local_doc_qa/get_doc_completed";
        JSONObject response = KnowledgeApiTool.post(apiUrl, requestBody);

        // 检查 API 响应状态码
        if (response.getInteger("code") == 200) {
            return CommonResult.success(response);
        } else {
            // 返回错误消息给前端，使用全局错误码处理
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, response.getString("msg")
            );
        }
    }

    /**
     * 点击文件生成知识
     * @param reqs 请求体，包含文件ID、知识库ID等信息
     * @return 生成知识的结果
     */
    @PostMapping("/generateKnowledge")
    @Operation(summary = "根据多个文件生成知识，传入多组文件对象信息")
    public CommonResult<String> generateKnowledge(@RequestBody List<FileGenerateKnowledgeReq> reqs) {
        // 循环处理每个文件的知识生成请求
        reqs.forEach(fileReq -> fileService.generateKnowledgeForFile(fileReq));
        return CommonResult.successWithMessageOnly("知识生成中，稍后将返回结果");
    }

    @GetMapping("/preview_url")
    @Operation(summary = "获取预览文件Url")
    public CommonResult<String> previewUrl(@RequestParam String filePath){
        return CommonResult.success(fileComponent.previewUrlByPath(filePath));
    }

    @GetMapping("/preview_url_by_id")
    @Operation(summary = "通过id获取预览文件Url")
    public CommonResult<String> previewUrlById(@RequestParam String id){
        Long fileId;
        try {
            fileId = Long.parseLong(id);
        } catch (NumberFormatException e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FAQ_FILE_ERROR, "文件预览异常");
        }
        String url = fileComponent.previewUrlById(fileId);
        return CommonResult.success(url);
    }

    @GetMapping("/preview")
    @Operation(summary = "通过id预览文件")
    public ResponseEntity<ByteArrayResource> preview(@RequestParam Long fileId){
        return fileService.previewById(fileId);
    }

    @GetMapping("/preview_path")
    @Operation(summary = "通过路径获取预览文件")
    public ResponseEntity<ByteArrayResource> previewFile(@RequestParam String filePath) throws IOException {
        if (filePath==null || filePath.isEmpty()){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, "文件路径");
        }
        // 获取当前存储服务
        FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
        return fileStorageService.previewFile(filePath);
    }

    @PostMapping("/previwPdf")
    @Operation(summary = "上传pdf文件")
    public CommonResult<UploadFileReps> previewPdf(@Valid MultipartFile file){
        UploadFileReps fileReps= fileService.uploadFile(file);
        String filePath = fileReps.getFilePath();
        fileReps.setFilePath(fileComponent.previewUrlByPath(filePath));
        return CommonResult.success();
    }

    @GetMapping("/showPdfMessage")
    @Operation(summary = "根据jobId获取pdf提取信息")
    public CommonResult<HashMap> previewPdfMessage(@Valid Long jobId){
        return CommonResult.success(fileService.pdfMessage(jobId));
    }
    @PostMapping("/getJobId")
    @Operation(summary = "获取jobId")
    public CommonResult<Long> getJobId(@RequestBody PdfReq pdfReq) {
        Long jobId = fileService.getJobId(pdfReq);
        return CommonResult.success(jobId);
    }

    @PostMapping("/getPdfMessage")
    @Operation(summary = "算法回调，获取pdf信息")
    public CommonResult<String> getPdfMessage(@RequestBody PdfMessageReq pdfMessageReq) {
        log.info("pdf信息:{}",pdfMessageReq);
        fileService.savePdfMessage(pdfMessageReq);
        return CommonResult.successWithMessageOnly("保存成功");
    }

    @PostMapping("/upload")
    @Operation(summary = "上传文件作为Logo")
    public CommonResult<Long> upload(@RequestParam MultipartFile file) throws IOException {
        Long id = fileService.upload(file);
        return CommonResult.success(id,"上传成功");
    }


    @GetMapping("/get_doc_chunk")
    @Operation(summary = "查看文件切分后的chunk")
    public CommonResult<PageResult<Chunk>> getDocChunk(DocChunkPageReq docChunkPageReq){
        return CommonResult.success(fileService.getDocChunk(docChunkPageReq));
    }

    @PostMapping("/update_doc_chunk")
    @Operation(summary = "修改文件的一个Chunk")
    public CommonResult<String> updateDocChunk(@RequestBody @Valid UpdateDocChunkReq updateDocChunkReq){
        fileService.updateDocChunk(updateDocChunkReq);
        return CommonResult.success();
    }
}