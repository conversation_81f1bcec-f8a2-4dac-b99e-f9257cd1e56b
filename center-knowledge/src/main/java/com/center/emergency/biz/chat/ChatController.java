package com.center.emergency.biz.chat;


import cn.hutool.core.lang.Snowflake;
import java.util.*;

import com.center.emergency.biz.chat.pojo.*;
import com.center.emergency.biz.chat.service.ChatService;
import com.center.emergency.biz.robot.pojo.RobotPreviewResp;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@Tag(name = "智能对话")
@RequestMapping("/chat")
@Validated
@Slf4j
public class ChatController {

    @Resource
    Snowflake snowflake;

    @Resource
    private ChatService chatService;

    @GetMapping("/init_session")
    @Operation(summary = "获取对话初始化sessionID")
    public CommonResult<Long> iniSession(){
        return CommonResult.success(snowflake.nextId());
    }

    @PostMapping(value = "/reanswer", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "机器人重新回答问题")
    public SseEmitter reAnswer(@RequestBody @Valid ReChatReq reChatReq){
        return chatService.reAnswer(reChatReq);
    }

    @PostMapping(value = "/chat_with_robot", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "与机器人进行对话")
    public SseEmitter chatWithRobot(@RequestBody @Valid ChatWithRobotReq chatWithRobotReq){
        return chatService.chatWithRobot(chatWithRobotReq,Boolean.FALSE);
    }

    @PostMapping(value = "/chat_with_knowledge_base", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "与知识库进行模拟对话")
    public SseEmitter chatWithKnowledgeBase(@RequestBody @Valid ChatWithKnowledgeReq chatWithRobotReq){
        return chatService.chatWithKnowledge(chatWithRobotReq);
    }

    @PostMapping(value = "/simulate_with_robot", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "模拟对话与机器进行")
    public SseEmitter simulateWithRobot(@RequestBody @Valid SimulateChatRobotReq simulateChatRobotReq){
        return chatService.simulatedChatWithRobot(simulateChatRobotReq);
    }


    @GetMapping(value = "/search_all_robot")
    @Operation(summary = "查询用户所在公司的所有机器人")
    public CommonResult<List<RobotPreviewResp>> searchAllRobot(){
        return CommonResult.success(chatService.searchAllRobot());
    }

    @GetMapping(value = "/search_all_session")
    @Operation(summary = "查询用户的历史会话（按日期分组）")
    @EnumConvertPoint
    public CommonResult<Map<String, List<ChatSessionResp>>> searchSession(@Valid ChatSessionSearchReq chatSessionSearchReq){
        return CommonResult.success(chatService.searchAllSession(chatSessionSearchReq));
    }

    @PostMapping(value = "/update_title")
    @Operation(summary = "更新机器人会话标题")
    public CommonResult<String> updateTitle(@RequestBody @Valid UpdateTitleReq updateTitleReq){
        chatService.updateTitle(updateTitleReq);
        return CommonResult.successWithMessageOnly("标题更新成功");
    }


    @GetMapping(value = "/search_session_history/{sessionId}")
    @Operation(summary = "查询历史对话的详情")
    public CommonResult<List<ChatHistoryResp>> searchSessionHistory(@PathVariable(name = "sessionId") Long sessionId){
        return CommonResult.success(chatService.searchSessionHistory(sessionId));
    }

    @GetMapping(value = "/search_last_session_history_by_robot/{robotId}")
    @Operation(summary = "根据机器人ID查询最后一次对话的历史详情")
    @EnumConvertPoint
    public CommonResult<Object> searchLastSessionHistoryByRobotId(@PathVariable(name = "robotId") Long robotId){
        RobotNewSessionChatHistoryResp result = chatService.searchLastSessionHistoryByRobotId(robotId);
        // 如果没有会话记录，返回空数组
        if (result == null) {
            return CommonResult.success(new ArrayList<>());
        }
        return CommonResult.success(result);
    }


    @PostMapping(value = "/thumbs_up/{id}")
    @Operation(summary = "给回答的内容点赞")
    public CommonResult<String> thumbsUp(@PathVariable(name = "id") Long id){
        chatService.thumbsUp(id);
        return CommonResult.success();
    }

    @PostMapping(value = "/thumbs_down/{id}")
    @Operation(summary = "给回答的内容点倒赞")
    public CommonResult<String> thumbsDown(@PathVariable(name = "id") Long id){
        chatService.thumbsDown(id);
        return CommonResult.success();
    }

    @PostMapping("/autoGenerate")
    @Operation(summary = "智能生成系统提示词/开场白")
    public CommonResult<String> autoGeneration(@Valid @RequestBody AutoGenerationReq autoGenerationReq) {
        return CommonResult.success(chatService.autoGeneration(autoGenerationReq));
    }

    @PostMapping("/quick_questions")
    @Operation(summary = "智能生成快捷问题")
    public CommonResult<List<String>> autoGenerateQuickQuestions(@Valid @RequestBody QuickQuestionsReq quickQuestionsReq) {
        return CommonResult.success(chatService.autoGenerateQuickQuestions(quickQuestionsReq));
    }

    @PostMapping("/delete_session")
    @Operation(summary = "删除会话")
    public CommonResult<String> deleteSession(@RequestParam(name = "sessionId") Long sessionId) {
        chatService.deleteSession(sessionId);
        return CommonResult.successWithMessageOnly("会话删除成功");
    }

    @GetMapping("/user_sessions_page")
    @Operation(summary = "分页查询用户的所有历史会话")
    @EnumConvertPoint
    public CommonResult<PageResult<UserChatSessionPageResp>> pageUserChatSessions(
            @Parameter(description = "页码，从1开始", example = "1")
            @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小值为 1") Integer pageNo,
            @Parameter(description = "每页条数，最大100", example = "10")
            @RequestParam(defaultValue = "10") @Min(value = 1, message = "每页条数最小值为 1") @Max(value = 100, message = "每页条数最大值为 100") Integer pageSize,
            @Parameter(description = "会话标题关键词（模糊搜索）", example = "产品咨询")
            @RequestParam(required = false) String keyword,
            @Parameter(description = "机器人名称关键词（模糊搜索）", example = "客服")
            @RequestParam(required = false) String robotName) {

        return CommonResult.success(chatService.pageUserChatSessions(pageNo, pageSize, keyword, robotName));
    }
}
