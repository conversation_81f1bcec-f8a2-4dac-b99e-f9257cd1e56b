package com.center.emergency.biz.chat.pojo;

import com.center.emergency.common.enumeration.AnswerModeEnum;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.emergency.common.enumeration.SearchModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SimulateChatRobotReq {

    @Schema(description = "机器人ID",example = "3")
    @NotNull(message = "机器人ID不能为空")
    private Long id;

    // 关联知识库
    @Schema(description = "关联Id列表，可能是MCP,数据库，知识库",example = "")
    private List<Long> answerStrategyIds;

    // 关联模型组ID
    @Schema(description = "机器人关联的模型组ID", example = "1001")
    private Long modelGroupId;

    // 检索模式
    @Schema(description = "检索模式：VECTOR / TEXT / HYBRID", example = "HYBRID")
    private SearchModeEnum searchMode;

    // 问答模式
    @Schema(description = "问答模式：ONLY_KB / KB_FIRST_MODEL / ONLY_QA", example = "KB_FIRST_MODEL")
    private AnswerModeEnum answerMode;

    // 文本匹配相似度阈值
    @Schema(description = "文本匹配相似度阈值", example = "0.20")
    private BigDecimal similarityThreshold;

    // 最大召回数量
    @Schema(description = "最大召回数量", example = "20")
    private Integer maxHits;

    @Schema(description = "Agent问答策略",example = "MCP")
    private AnswerStrategyEnum answerStrategy;

    @Schema(description = "问题内容")
    @NotBlank(message = "问题不能为空")
    private String question;

    @Schema(description = "用户自定义提示词")
    private String userCustomPrompt;

    // 上下文记忆条数
    @Schema(description = "上下文记忆条数", example = "5")
    @Min(value = 0, message = "上下文记忆条数不能小于0")
    @Max(value = 99, message = "上下文记忆条数不能大于99")
    private Integer historyWindowSize;

    @Schema(description = "是否启用模型路由，1为是，0为否")
    private Integer enableModelRouter;

    @Schema(description = "是否启用推理模式，1为是，0为否")
    private Integer enableReasoning;

    @Schema(description = "模型ID")
    private Long modelId;
}
