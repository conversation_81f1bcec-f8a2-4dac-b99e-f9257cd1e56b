package com.center.emergency.biz.modelgroup.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ModelGroupMemberRepository extends JpaRepository<ModelGroupMember, Long> {

    void deleteByModelGroupId(Long id);

    List<ModelGroupMember> findByModelGroupId(Long id);

    boolean existsByModelGroupIdAndLargeModelId(Long modelGroupId, Long largeModelId);
}
