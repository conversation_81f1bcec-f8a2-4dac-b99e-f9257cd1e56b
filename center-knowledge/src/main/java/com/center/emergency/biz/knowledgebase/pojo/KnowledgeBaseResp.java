package com.center.emergency.biz.knowledgebase.pojo;

import com.center.emergency.biz.tag.pojo.TagResp;
import com.center.emergency.common.enumeration.KnowledgeTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 响应对象：返回知识库的详细信息
 */
@Data
public class KnowledgeBaseResp {

    @Schema(description = "知识库ID", example = "123")
    private Long id;

    @Schema(description = "知识库名称", example = "知识库A")
    private String kbName;

    @Schema(description = "所属部门ID", example = "123")
    private Long departmentId;

    @Schema(description = "部门名称", example = "123")
    private String departmentName;

    @Schema(description = "模型文件库ID", example = "fileb_12345")
    private String aiFilebId;

    @Schema(description = "模型知识库ID", example = "faqb_12345")
    private String aiFaqbId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2023-10-12 08:23:45")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2023-10-12 08:23:45")
    private LocalDateTime updateTime;

    @Schema(description = "标签列表")
    private List<TagResp> tags;

    @Schema(description = "知识库类型")
    private KnowledgeTypeEnum knowledgeType;
}