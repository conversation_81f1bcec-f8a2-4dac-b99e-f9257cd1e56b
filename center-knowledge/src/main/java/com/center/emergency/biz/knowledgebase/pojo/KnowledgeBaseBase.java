package com.center.emergency.biz.knowledgebase.pojo;

import com.center.emergency.common.enumeration.KnowledgeTypeEnum;
import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
/**
 * 基础POJO类：用于知识库相关的基础字段
 */
@Data
public class KnowledgeBaseBase {

    @Schema(description = "知识库名称", example = "知识库A")
    @NotNull(message = "知识库名称不能为空")
    @Length(min = 2, max = 100, message = "知识库名称长度应在2到100个字符之间")
    private String kbName;

    @Schema(description = "所属部门ID")
    @NotNull(message = "部门ID不能为空")
    private Long departmentId;

    @Schema(description = "租户ID",requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    private Long tenantId;

    @Schema(description = "知识库类型",example = "ENTERPRISE/INDIVIDUAL")
    @EnumValidate(message = "知识库类型不正确",value = KnowledgeTypeEnum.class)
    private String kbType = "ENTERPRISE"; // 默认为企业知识库

}