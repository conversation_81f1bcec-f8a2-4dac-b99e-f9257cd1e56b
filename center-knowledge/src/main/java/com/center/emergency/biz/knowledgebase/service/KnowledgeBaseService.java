package com.center.emergency.biz.knowledgebase.service;




import com.center.emergency.biz.knowledgebase.pojo.*;
import com.center.emergency.common.enumeration.KnowledgeTypeEnum;

import java.util.List;


/**
 * 知识库服务接口：定义知识库的业务逻辑
 */
public interface KnowledgeBaseService {

    /**
     * 创建知识库
     *
     * @param req 创建知识库的请求对象
     * @return 知识库的详细信息
     */
    void createKnowledgeBase(KnowledgeBaseCreateReq req);

    /**
     * 更新知识库
     *
     * @param req 更新知识库的请求对象
     * @return 知识库的详细信息
     */
    KnowledgeBaseResp updateKnowledgeBase(KnowledgeBaseUpdateReq req);

    /**
     * 删除知识库（逻辑删除）
     *
     * @param id 知识库ID
     */
    void deleteKnowledgeBase(Long id);

    /**
     * 根据ID获取知识库的详细信息
     *
     * @param id 知识库ID
     * @return 知识库的详细信息
     */
    KnowledgeBaseResp getKnowledgeBaseByIdWithTags(Long id);

    /**
     * 根据部门ID和租户ID查询知识库，校验权限
     *
     * @param deptId 目标部门ID
     * @param path
     * @param kbType 知识库类型
     * @return 知识库响应列表
     */
    List<KnowledgeBaseResp> getKnowledgeBasesByDeptId(Long deptId, String path, KnowledgeTypeEnum kbType);

    /**
     * 根据查询条件查询知识库列表
     * @param queryReq 目标部门ID
     *      * @return 知识库响应列表
     */
    List<KnowledgeBaseResp> getKnowledgeBases(KnowledgeBaseQueryReq queryReq);


    /**
     * 根据传入的字段模糊查询 部门-知识库
     *
     * @param keyWord
     * @return SearchResult列表
     */
    SearchResult searchKBases(String keyWord);
}