package com.center.emergency.biz.knowledge.persistence;

import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.framework.db.core.BaseDeleteModel;
import com.center.framework.db.core.BaseModel;
import com.center.framework.db.core.BaseTenantModel;
import com.center.infrastructure.system.biz.tenant.persitence.TenantModel;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;

import javax.persistence.*;

/**
 * 知识实体类（KnowledgeModel）
 * <AUTHOR>
 */
@Data
@Entity
@QueryEntity
@Table(name = "center_knowledges")
public class KnowledgeModel extends BaseTenantModel {

    // 知识库ID
    @Column(name = "kb_id", nullable = false)
    private Long kbId;

    // 关联文件ID
    @Column(name = "file_id")
    private Long fileId;

    // 模型知识库的知识ID
    @Column(name = "ai_faqb_faq_id", nullable = false)
    private Long aiFaqbFaqId;

    // 模型文件库的文件ID
    @Column(name = "ai_fileb_file_id")
    private Long aiFilebFileId;

    // 知识名称
    @Column(name = "knowledge_name", nullable = false, length = 100)
    private String knowledgeName;

    // 知识状态
    @Column(name = "status", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private KnowledgeStatusEnum status;

}