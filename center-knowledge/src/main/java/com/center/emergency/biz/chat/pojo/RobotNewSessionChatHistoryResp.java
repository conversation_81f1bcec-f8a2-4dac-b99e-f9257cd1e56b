package com.center.emergency.biz.chat.pojo;

import com.center.emergency.common.enumeration.ChatTypeEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 机器人最新会话对话历史响应对象
 */
@Data
@Schema(description = "机器人最新会话对话历史响应对象")
public class RobotNewSessionChatHistoryResp {

    @Schema(description = "会话ID")
    private Long sessionId;

    @Schema(description = "会话标题")
    private String title;

    @Schema(description = "机器人ID")
    private Long robotId;

    @Schema(description = "对话类型", example = "NORMAL")
    private ChatTypeEnum chatType;

    @EnumConvert(value = ChatTypeEnum.class, srcFieldName = "chatType")
    @Schema(description = "对话类型名称", example = "普通对话")
    private String chatTypeName;

    @Schema(description = "会话创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "最新活动时间（最新回答时间或会话创建时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastActivityTime;

    @Schema(description = "对话历史详情列表")
    private List<ChatHistoryResp> chatHistoryList;
}
