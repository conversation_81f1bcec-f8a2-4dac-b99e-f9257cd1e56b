package com.center.emergency.biz.robot.service;

import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.framework.common.pojo.IdAndValue;
import com.querydsl.jpa.impl.JPAQueryFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AnswerStrategyService {

    /**
     * 创建Agent时，需要初始化Agent的回答策略
     * @param robotId
     * @param answerStrategy
     */
    void createRobotAnswerStrategy(Long robotId, AnswerStrategyEnum answerStrategy);

    /**
     * 更新Agent的回答策略
     * @param robotId
     * @param answerStrategy
     * @param answerStrategyIds 最新的id列表，这些id可能是MCP，也可能是KB或者是文件和数据库的
     */
    void updateRobotAnswerStrategy(Long robotId, AnswerStrategyEnum answerStrategy, List<Long> answerStrategyIds);

    /**
     * 删除机器人时，需要删除机器人对应的回答策略
     * @param robotId
     */
    void deleteAllWithRobotId(Long robotId);

    /**
     * 获取机器人对应的回答策略id列表
     * @param robotId
     * @return
     */
    List<Long> listAnswerStrategyIds(Long robotId);

    /**
     * 构建对话参数 - 根据不同的答案策略构建不同的参数
     * @param chatVO 对话VO
     * @param kbIds 知识库ID集合
     * @param fileIds 文件ID列表
     * @param tags 标签列表
     * @param isSimulate 是否模拟
     * @param modelId 模型ID（用于查询个性化配置）
     * @param jpaQueryFactory JPA查询工厂
     * @return 请求参数映射
     */
    HashMap<String, Object> buildChatParams(ChatVO chatVO, Set<String> kbIds, List<String> fileIds,
                                           List<Map<String, String>> tags, Boolean isSimulate,
                                           Long modelId, JPAQueryFactory jpaQueryFactory);

    /**
     * 构建对话参数 - 策略自主收集资源版本
     * 每个策略自己负责收集所需的资源，实现更好的职责分离
     * @param chatVO 对话VO
     * @param isSimulate 是否模拟
     * @param modelGroupId 模型组ID（用于查询模型组配置）
     * @param jpaQueryFactory JPA查询工厂
     * @return 请求参数映射
     */
    HashMap<String, Object> buildChatParamsWithResourceCollection(ChatVO chatVO, Boolean isSimulate,
                                                                 Long modelGroupId, JPAQueryFactory jpaQueryFactory,Long modelId);

    void addModelGroupParams(HashMap<String, Object> param, Long modelGroupId, Long modelId);
}
