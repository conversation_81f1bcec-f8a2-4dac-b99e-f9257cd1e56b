package com.center.emergency.biz.robot.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotBlank;


@Data
public class RobotBase {

    // 机器人名称
    @NotBlank(message = "机器人名称不能为空")
    @Length(max = 20, message = "机器人名称长度不能超过20个字符")
    private String robotName;

    // 机器人欢迎语
    @Schema(description = "机器人欢迎语",example = "你好，请问有什么需要帮助的吗？")
    @Length(max = 1000, message = "机器人欢迎语长度不能超过1000个字符")
    private String welcomeMessage;

    // 机器人备注
    @Schema(description = "备注",example = "这是一个机器人")
    @Length(max = 200, message = "机器人备注长度不能超过200个字符")
    private String remark;

}
