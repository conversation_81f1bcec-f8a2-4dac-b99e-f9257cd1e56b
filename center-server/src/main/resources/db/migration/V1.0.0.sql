-- 由于一期依赖了大数据平台，数据库初始化由大数据平台做的，二期不依赖了，数据库依赖的sql拿过来执行 --
-- 1.0.1--
CREATE TABLE `center_system_tenant` (
                                        `id` bigint(20) NOT NULL  COMMENT '租户ID',
                                        `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户名',
                                        `description` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '备注说明',
                                        `status` varchar(10) NOT NULL COMMENT '租户状态',
                                        `expire_time` datetime NOT NULL COMMENT '过期时间',
                                        `account_count` int(11) NOT NULL COMMENT '账号数量',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                        `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户表';


CREATE TABLE `center_system_user` (
                                      `id` bigint(20) NOT NULL COMMENT '用户ID',
                                      `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户账号',
                                      `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
                                      `display_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
                                      `status` varchar(10) NOT NULL COMMENT '帐号状态',
                                      `login_ip` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '最后登录IP',
                                      `login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                      `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                      `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

CREATE TABLE `center_system_role` (
                                      `id` bigint(20) NOT NULL COMMENT '角色ID',
                                      `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
                                      `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色代码',
                                      `sort` int(11) NOT NULL COMMENT '显示顺序',
                                      `status` varchar(10) DEFAULT NULL COMMENT '角色状态',
                                      `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                      `tenant_id` bigint(20) NOT NULL COMMENT '租户编号',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                      `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色信息表';


CREATE TABLE `center_system_menu` (
                                      `id` bigint(20) NOT NULL COMMENT '菜单ID',
                                      `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
                                      `category` varchar(10) NOT NULL COMMENT '菜单类型',
                                      `sort` int(11) NOT NULL DEFAULT '0' COMMENT '显示顺序',
                                      `parent_id` bigint(20) NOT NULL  COMMENT '父菜单ID',
                                      `path` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '路由地址',
                                      `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '#' COMMENT '菜单图标',
                                      `status` varchar(10) DEFAULT NULL COMMENT '菜单状态',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                      `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单表';

CREATE TABLE `center_system_user_role` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                           `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                           `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                           `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户和角色关联表';



CREATE TABLE `center_system_role_menu` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                           `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                           `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                           `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和菜单关联表';

INSERT INTO center_system_tenant
(id, name, description, status, expire_time, account_count, create_time, update_time, creator_id, updater_id)
VALUES(1825838985241448448, '深圳大数据研院无锡创新中心', '深圳大数据研院无锡创新中心', 'ACTIVE', '2099-12-31 23:59:59', 100, current_timestamp, current_timestamp, **********497688576, **********497688576);

INSERT INTO `center_system_user`
(id, username, password, display_name, status, login_ip, login_time, create_time, update_time, creator_id, updater_id, tenant_id)
VALUES(**********497688576, 'superadmin', '867b67919f24871f189784d933d558df', '超级管理员', 'ACTIVE', NULL, NULL, current_timestamp,current_timestamp,**********497688576, **********497688576, 1825838985241448448);

INSERT INTO center_system_role
(id, name, code, sort, status, remark, tenant_id, create_time, update_time, creator_id, updater_id)
VALUES(1826081597714087936, '超级管理员', 'SUPERADMIN', 1, 'ACTIVE', '系统超级管理员', 1825838985241448448,current_timestamp,current_timestamp, **********497688576, **********497688576);

INSERT INTO center_system_user_role
(id, user_id, role_id, create_time, update_time, creator_id, updater_id)
VALUES(1826082885449302016, **********497688576, 1826081597714087936, current_timestamp,current_timestamp, **********497688576, **********497688576);



-- 1.0.5 --
CREATE TABLE `center_datasource_task` (
                                          `id` bigint(20) NOT NULL COMMENT '主键',
                                          `datasource_id` bigint(20) NOT NULL COMMENT '数据源ID',
                                          `task_code` varchar(50) NOT NULL COMMENT '任务Code',
                                          `task_execute_type` varchar(20) NOT NULL COMMENT '任务执行方式BATCH/STREAM',
                                          `task_running_strategy` varchar(20) NOT NULL COMMENT '任务运行策略IMMEDIATELY/SCHEDULE/NONE',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                          `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据源与任务关系表';


CREATE TABLE `center_table_task` (
                                     `id` bigint(20) NOT NULL COMMENT '主键',
                                     `table_name` varchar(100) NOT NULL COMMENT '表名称',
                                     `task_code` varchar(50) NOT NULL COMMENT '任务Code',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                     `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据表与任务关系表';

-- 1.0.6 --
-- center_task_cron definition

CREATE TABLE `center_task_cron` (
                                    `id` bigint(20) NOT NULL COMMENT '主键ID',
                                    `task_code` varchar(100) NOT NULL COMMENT '任务Code',
                                    `day_of_week` varchar(100) DEFAULT NULL COMMENT '一周中的那几天',
                                    `day_of_month` varchar(100) DEFAULT NULL COMMENT '一月中的哪几天',
                                    `month` varchar(100) DEFAULT NULL COMMENT '哪几个月',
                                    `second` int(2) DEFAULT NULL COMMENT '秒',
                                    `minute` int(2) DEFAULT NULL COMMENT '分钟',
                                    `hour` int(2) DEFAULT NULL COMMENT '小时',
                                    `job_type` varchar(20) DEFAULT NULL COMMENT '任务类型',
                                    `cron_expression` varchar(100) NOT NULL COMMENT 'cron表达式',
                                    `start_time` datetime COMMENT '任务开始时间',
                                    `end_time` datetime COMMENT '任务结束时间',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                    `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 1.0.7 --
ALTER TABLE center_datasource_task ADD target_database varchar(500) NULL COMMENT '目标数据库或文件夹';


-- 1.0.8 --
ALTER TABLE center_table_task ADD resource_type varchar(20) NULL COMMENT '资源类型：表/文件';

-- 1.0.9 --
CREATE TABLE `center_table_history`
(
    `id`          bigint(20) NOT NULL COMMENT '主键id',
    `task_code`   varchar(50)  NOT NULL COMMENT '任务Code',
    `task_name`   varchar(100) NOT NULL COMMENT '任务名称',
    `alarm_type`  varchar(100) NOT NULL COMMENT '告警类型',
    `content`     varchar(1000) NOT NULL COMMENT '内容',
    `total_count` bigint(20) DEFAULT NULL COMMENT 'Total count',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='任务历史消息表';

-- 1.0.10 --
ALTER TABLE center_task_cron ADD be_apart int(2) NULL COMMENT '间隔';

