spring:
  datasource:
    url: *****************************************************************************************************************************************************
    username: nsodev
    password: Nsodev#2025@

  flyway:
    url: *******************************************************************************************************************************************
    user: nsodev
    password: Nsodev#2025@
    table: knowledge_flyway_schema_history
  #    flyway元数据表名称，如果不指定，则默认为flyway_schema_history，多个系统共用一个库时，可以通过使用不再的表来控制数据库脚本的版本。
  #    同一个表的元数据，最好是由一个系统进行维护
  # Redis 配置
  redis:
    host: 127.0.0.1 # 地址
    port: 6379 # 端口
    database: 7 # 数据库索引
    password: admin123 # 密码，建议生产环境开启
  #排除redis自动配置类，避免redis自动创建，当启用redis时注释掉下方代码
  autoconfigure:
    exclude:
      - org.redisson.spring.starter.RedissonAutoConfiguration

#  -- #################### 接口文档配置（生产环境关闭-false） ####################
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
###################业务逻辑相关配置##########################

# 文件预览返回获取流式文件的URL
server:
  preview_by_url: http://***********.6:9092/knowledge2/file/preview_path

#hdfs配置
hadoop:
  fs:
    defaultFS: hdfs://**********:8088
  dfs:
    client:
      use-datanode-hostname: true
  user:
    name: root
  home:
    dir: /usr/local/hadoop-3.4.1
  base:
    url: http://**********:8088/webhdfs/v1
    query: ?op=OPEN
  base-path: /knowledge2


file:
  localPath: /usr/local/backend/knowledge/temp
#调用算法接口
python:
  api-base-url: http://**********:8779
  api-base-dfsurl: http://**********:8088/webhdfs/v1
  api-url-upload: http://*************:8670/process_file/
  api-url-process: http://*************/v1/workflows/run
  api-key: app-i8p2B40OaL67Qy1WDvc9KvbB
  #这个接口算法那边暂时不用
  chat-api-base-url: http://********:8901/v1

#cache工厂类使用，目前支持redis（中央缓存）或memory(本地缓存)
cache:
  # 缓存类型，可选值：memory, redis
  type: memory

#创建企业管理员用户初始密码
admin:
  password: e10adc3949ba59abbe56e057f20f883e
password:
  default: 123456
# 或者 minio, ftp, oss 等
storage:
  type: MINIO
#minio配置
minio:
  # MinIO的服务地址
  endpoint: http://**********:19100
  downloadEndpoint: http://**********:19100
  #生产环境的minIO不支持https，只能通过nginx进行转发。
  downloadEndpointNginx: http://112.25.82.233:9083/oss-platform
  # MinIO的访问密钥
  access-key: minioadmin
  # MinIO的秘密密钥
  secret-key: minioadmin
  bucket: knowledge2


node:
  dir: /usr/local/backend/parse_pdf/main.js

# nginx转发实现文件预览配置
nginx:
  previewUrl: http://112.25.82.234:8000/images/knowledge/
  localPath: /usr/local/nginx/html/images/knowledge/
# AI搜索模块配置
ai:
  search:
    # 全网搜索算法接口URL
    global:
      url: http://*************:8779/api/local_doc_qa/ai_search_and_chat
    # 企业内部搜索算法接口URL
    internal:
      url: http://*************:8779/api/local_doc_qa/local_doc_chat
qa:
  # 默认的问答算法接口URL
  updateUrl: http://*************:8779/api/local_doc_qa/upload_faqs
  # QA重复性检查接口URL
  checkDuplicatesUrl: http://*************:8779/api/local_doc_qa/check_faqs_duplicates